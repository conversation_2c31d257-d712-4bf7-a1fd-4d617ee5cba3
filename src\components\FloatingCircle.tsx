
interface FloatingCircleProps {
  icon: string; 
  title: string;
  subtitle: string;
  sizeClasses: string;
  positionClasses: string; 
  animationClass: string;
  animationDelay?: string;
  colorText?:string 
}

const FloatingCircle: React.FC<FloatingCircleProps> = ({
  icon,
  title,
  subtitle,
  sizeClasses,
  positionClasses,
  animationClass,
  animationDelay,
  colorText
}) => {

  return (
    <div  
      className={`
        min-[1260px]:absolute  rounded-full shadow-xl flex flex-col items-center justify-center text-center delay-[${animationDelay || '0s'}]
        ${sizeClasses} ${positionClasses} ${animationClass} min-[1260px]:outline-8 outline-white 
      `}
    >
      <img src={icon} alt="icon"  loading="lazy"/>
      <h3 className={`text-sm xl:text-base font-extrabold ${colorText}`}>{title}</h3>
      <p className="text-secondary-03 text-sm xl:text-base">{subtitle}</p>
    </div>
  );
};

export default FloatingCircle;