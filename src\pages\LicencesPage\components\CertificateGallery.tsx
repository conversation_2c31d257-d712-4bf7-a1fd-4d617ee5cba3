import SectionTitle from "../../../components/SectionTitle";

interface Certificate {
  src: string;
  alt: string;
}

interface CertificateGalleryProps {
  title: string;
  certificates: Certificate[];
}

const CertificateGallery: React.FC<CertificateGalleryProps> = ({ title, certificates }) => {
  return (
    <section className=" 2xl:container mx-auto  py-16 px-4 md:px-[60px] xl:px-main-padding-large">
        <SectionTitle title={title} className='text-center w-fit gap-1 md:gap-3'/>
        <div className="grid grid-cols-1 sm:grid-cols-2  gap-8">
          {certificates.map((cert, index) => (
            <div data-aos="fade-up"
              key={index} 
              className="bg-white p-4 rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 group"
            >
              <img
                src={cert.src}
                alt={cert.alt}
                className="w-full h-full object-contain transform group-hover:scale-105 transition-transform duration-300"
                loading="lazy"
              />
            </div>
          ))}
        </div>
    </section>
  );
};

export default CertificateGallery;