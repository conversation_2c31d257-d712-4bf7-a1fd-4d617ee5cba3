import ProductCard from './CategoryProductsCard';
import type { ProductForMenu  } from '../../../services/api/use-type-api';

const ProductsSection: React.FC<{ products: ProductForMenu[] }> = ({ products }) => {
  return (
    <section className="2xl:container mx-auto p-5 md:p-[60px] xl:p-main-padding-large">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-9 ">
          {products.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
    </section>
  );
};

export default ProductsSection;