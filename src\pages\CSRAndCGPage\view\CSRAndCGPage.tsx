import { useState } from "react";
import CommonHero from "../../../sections/CommonHero";
import {
  dynamicSlidesData,
  infoSectionsData,
} from "../../../data/CsrAndCgData";
import InfoSectionDisplay from "../components/InfoSectionDisplay";

const CSRAndCGPage = () => {
  const [activeIndex, setActiveIndex] = useState(0);

  return (
    <div>
      <CommonHero
        slides={dynamicSlidesData}
        dynamicText={true}
        subHero={true}
        isCenter={true}
        onSlideChange={setActiveIndex}
      />

      <InfoSectionDisplay sections={infoSectionsData[activeIndex]} />
    </div>
  );
};

export default CSRAndCGPage;
