import { coreValuesData } from "../../../../data/CoreValueData";
import coreValuesImg from "../../../../assets/images/aboutPage/core-values.png";
import SectionTitle from "../../../../components/SectionTitle";
import { ValueCard } from "./CoreValueCard";

const CoreValuesSection: React.FC = () => {
  return (
    <section className="bg-white p-5 md:p-[60px] xl:p-main-padding-large">
      <SectionTitle
        title="Core Values"
        className="items-center w-fit gap-1 md:gap-3"
      />
      <div className="2xl:container mx-auto mt-10">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12 items-center">
          <div className="hidden lg:flex">
            <img src={coreValuesImg} alt="core Values Img" />
          </div>
          <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
            {coreValuesData.map((value, index) => (
              <ValueCard key={index} {...value} />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default CoreValuesSection;
