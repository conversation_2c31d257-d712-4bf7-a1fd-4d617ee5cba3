
interface TextBlockProps {
  title: string;
  description: string;
  className?: string;
}

const TextBlock: React.FC<TextBlockProps> = ({ title, description, className }) => {
  return (
    <div className={className}>
      <h3 className="font-bold text-base lg:text-lg xl:text-2xl text-primary-01">{title}</h3>
      <p className="md:text-[12px] lg:text-sm xl:text-xl text-secondary-01 mt-1 lg:mt-2 ">{description}</p>
    </div>
  );
};

export default TextBlock;