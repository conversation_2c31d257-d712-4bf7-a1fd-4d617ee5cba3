import { faEnvelope, faPhoneVolume, faLocationDot } from '@fortawesome/free-solid-svg-icons';
import type { IconDefinition } from '@fortawesome/free-solid-svg-icons';

interface FormField {
  id: string;
  name: string;
  placeholder: string;
  type: 'text' | 'email' | 'tel' | 'select' | 'textarea';
  required: boolean;
  options?: { value: string; label: string }[];
}

interface ContactInfo {
    icon: IconDefinition;
    title: string;
    details: string;
}



export const formFields: FormField[] = [
  { id: 'name', name: 'name', placeholder: 'Name *', type: 'text', required: true },
  { id: 'email', name: 'email', placeholder: 'Email *', type: 'email', required: true },
  { id: 'phone', name: 'phone', placeholder: 'Phone number *', type: 'tel', required: true },
  {
    id: 'howDidYouFindUs',
    name: 'finding_way',
    placeholder: 'How did you find us?',
    type: 'select',
    required: true,
    options: [
      { value: 'facebook', label: 'Facebook' },
      { value: 'whatsapp', label: 'Whatsapp' },
      { value: 'instagram', label: 'Instagram' },
      { value: 'linkedin', label: 'LinkedIn' },
      { value: 'friend', label: 'From a friend' },
      { value: 'other', label: 'Other' },
    ],
  },
  { id: 'message', name: 'message', placeholder: 'Your Message *', type: 'textarea', required: true },
];


export const contactInfo: ContactInfo[] = [
    { icon: faPhoneVolume, title: 'PHONE', details: '+971 4 266 7172' },
    { icon: faEnvelope, title: 'EMAIL', details: '<EMAIL>' },
    { icon: faLocationDot, title: 'LOCATION', details: 'Dubai - UAE' },
];