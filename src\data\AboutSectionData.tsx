import EquipmentIcon from "../assets/images/mainPage/about-icon1.svg";
import ExperienceIcon from "../assets/images/mainPage/about-icon2.svg";
import TeamIcon from "../assets/images/mainPage/about-icon3.svg";

export interface CircleData {
  id: number;
  icon: string;
  title: string;
  subtitle: string;
  sizeClasses: string;
  positionClasses: string;
  animationClass: string;
  animationDelay?: string;
  colorText?: string;
}

export const circlesData: CircleData[] = [
  {
    id: 1,
    icon: TeamIcon,
    title: "Team",
    subtitle: "Professionals",
    sizeClasses: "w-[146px] h-[146px] p-4 xl:px-[18px] xl:py-6  bg-white",
    positionClasses: "lg:top-6 lg:-left-5 xl:left-0",
    animationClass: "floating-light",
    animationDelay: "0s",
    colorText: "text-primary-01 ",
  },
  {
    id: 2,
    icon: ExperienceIcon,
    title: "Experience",
    subtitle: "25 years of activity",
    sizeClasses: "w-[146px] h-[146px] px-[18px] py-6 bg-primary-01  ",
    positionClasses: "",
    animationClass: "floating-light",
    animationDelay: "0.2s",
    colorText: "text-white",
  },
  {
    id: 3,
    icon: EquipmentIcon,
    title: "State of the art",
    subtitle: "Equipment",
    sizeClasses: "w-[146px] h-[146px] px-[18px] py-6 bg-white",
    positionClasses: "lg:top-[30%] lg:left-3/4 xl:left-[80%] 2xl:left-[70%]",
    animationClass: "floating-light",
    animationDelay: "0.5s",
    colorText: "text-primary-01 ",
  },
];

export const whatWeDoList = [
  "Conveyor System Design & Manufacturing.",
  "Industrial Processing Solutions.",
  "Engineering Safety Controls.",
  "Non-Structural Steel Fabrication.",
  "Product Lifecycle Management.",
  "Ergonomic Fit-Out and Renovation Services",
];
