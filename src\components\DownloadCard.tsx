import DownloadButton from './DownloadButton';
import { faFilePdf, faFileZipper } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

interface DownloadCardProps {
  title: string;
  description: string;
  filePath: string;
  fileName: string;
  fileType: 'pdf' | 'zip';
}

const DownloadCard: React.FC<DownloadCardProps> = ({ title, description, filePath, fileName, fileType }) => {
  const icon = fileType === 'pdf' ? faFilePdf : faFileZipper;

  return (
    <div className="flex flex-col rounded-lg bg-white p-6 shadow-lg transition-all duration-300 hover:shadow-xl hover:-translate-y-2">
      <div className="flex-grow">
        <FontAwesomeIcon icon={icon} className="text-3xl text-primary-01 mb-4" />
        <h3 className="text-xl font-bold text-secondary-01">{title}</h3>
        <p className="mt-2 text-base text-secondary-03">{description}</p>
      </div>
      <div className="mt-6">
        <DownloadButton 
          filePath={filePath}
          fileName={fileName}
          buttonText="Download Now"
          className="w-full"
        />
      </div>
    </div>
  );
};

export default DownloadCard;