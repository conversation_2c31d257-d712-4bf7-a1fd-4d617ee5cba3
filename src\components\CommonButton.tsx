import { Link } from "react-router-dom"
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
interface CommonButtonProps {
  path?:string;
  text?:string;
  style: string;
  textColor?: string
}

const CommonButton = ({path, text, style, textColor}: CommonButtonProps) => {
  return (
      <Link to={`${path}`} 
            className={`${style} ${textColor} flex items-center justify-center text-base rounded-full opacity-95 hover:opacity-100 transition-all duration-300 ease-in-out`}
      >
        {text}
        <FontAwesomeIcon icon={faArrowRight} className={`${textColor} h-5 w-5 ml-2 animate-wiggle`}/>
      </Link>
  )
}

export default CommonButton
