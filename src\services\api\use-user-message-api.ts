import useApiServices from "../hooks/use-api-services";
import axiosInstance from "../../utils/axios";

export const UserMessageEndpoints = {
  create: "/user-messages",
};

export interface CreateUserMessageRequest {
  name: string;
  phone: string;
  email: string;
  message: string;
  finding_way: string;
}

export interface UserMessageResponse {
    success: boolean;
    message: string;
}

export const useUserMessageApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  const useCreateUserMessage = (onSuccess?: (data: UserMessageResponse) => void) => {
    return apiServices.usePostService<CreateUserMessageRequest, UserMessageResponse>({
      url: UserMessageEndpoints.create,
      onSuccess,
      withFormData: true, 
    });
  };

  return {
    useCreateUserMessage,
  };
};