import type { Breadcrumb } from '../../../types/product-types';

interface BreadcrumbsProps {
  crumbs: Breadcrumb[];
}

export const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ crumbs }) => {
  return (
    <div className="flex items-center text-sm md:text-lg xl:text-2xl text-secondary-01 flex-wrap">
      {crumbs.map((crumb, index) => {
        const isLast = index === crumbs.length - 1;

        return (
          <div key={index} className="flex items-center">
            {isLast  || !crumb.url ? (
              <span className="text-primary-01 font-semibold">{crumb.name}</span>
            ) : (
              <a href={crumb.url} className="hover:text-primary-01">
                {crumb.name}
              </a>
            )}
            {!isLast && <span className="mx-2">&gt;</span>}
          </div>
        );
      })}
    </div>
  );
};