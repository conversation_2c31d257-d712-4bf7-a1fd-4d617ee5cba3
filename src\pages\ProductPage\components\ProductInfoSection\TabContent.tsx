
import type { ProductInfoTab } from './ProductInfoSection';
import ListItem from './ListItem';
import RenderImages from './RenderImages';
import type { ProductApplication, ProductKeyBenefit, ProductMedia, ProductSpecification } from '../../../../services/api/use-product-api';

interface TabContentProps {
  activeTab?: ProductInfoTab;
  promoImages: ProductMedia[];
  videoUrl: string;
}

export const TabContent: React.FC<TabContentProps> = ({ activeTab, promoImages, videoUrl }) => {
  if (!activeTab) {
    return (
      <div className="flex items-center justify-center rounded-lg bg-gray-50 p-10 mt-8">
        <p className="text-gray-500">Please select a tab.</p>
      </div>
    );
  }

  if (activeTab.id === 'description' && typeof activeTab.content === 'string') {
    return (
      <div className="mt-8 space-y-6 prose lg:prose-xl max-w-none">
        <p>{activeTab.content}</p>
      </div>
    );
  }

  if (activeTab.id === 'specification' && Array.isArray(activeTab.content)) {
    return (
        <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-10 items-center">
            <div className="w-full">
                <ul className="space-y-2">
                    {(activeTab.content as ProductSpecification[]).map((item, index) => (
                        <ListItem key={index} item={item} index={index} />
                    ))}
                </ul>
            </div>
            <div className="w-full">
                <RenderImages 
                  images={promoImages} 
                  imageLayout="video_promo" 
                  videoUrl={videoUrl}
                />
            </div>
        </div>
    );
  }

  if (activeTab.id === 'keyBenefits' && Array.isArray(activeTab.content)) {
      const firstImage = (activeTab.content[0] as ProductKeyBenefit)?.media ? [(activeTab.content[0] as ProductKeyBenefit).media] : [];
      return (
          <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-10 items-center">
              <div className="w-full">
                  <RenderImages images={firstImage} imageLayout="single" />
              </div>
              <div className="w-full">
                  <ul className="space-y-3">
                      {(activeTab.content as ProductKeyBenefit[]).map((item, index) => (
                          <ListItem key={index} item={item} index={index} />
                      ))}
                  </ul>
              </div>
          </div>
      );
  }
  
  if (activeTab.id === 'applications' && Array.isArray(activeTab.content)) {
    const allImages = (activeTab.content as ProductApplication[]).flatMap(app => app.media);
    return (
        <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-10 items-center">
            <div className="w-full">
                <ul className="space-y-3">
                    {(activeTab.content as ProductApplication[]).map((item, index) => (
                        <ListItem key={index} item={item} index={index} />
                    ))}
                    </ul>
            </div>
            <div className="w-full">
                <RenderImages images={allImages} imageLayout="multiple" />
            </div>
        </div>
    );
  }

  return <div className="mt-12"></div>;
};