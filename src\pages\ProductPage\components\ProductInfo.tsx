
import SectionTitle from '../../../components/SectionTitle';
import CommonButton from '../../../components/CommonButton';

interface ProductCategory {
  name: string;
  url: string;
}

interface ProductTag {
  name: string;
  url: string;
}

interface ProductInfoProps {
  name: string;
  description: string;
  categories: ProductCategory[];
  tags: ProductTag[];
}

export const ProductInfo: React.FC<ProductInfoProps> = ({ name, description, categories, tags }) => {
  return (
    <div className="flex flex-col max-sm:w-full max-lg:w-3/4 items-start">
      <div className="mb-4">
        <SectionTitle title={name} info={description} className='text-start w-fit gap-1 md:gap-3' descStyle='text-sm md:text-base xl:text-2xl'/>
      </div>
       <CommonButton path='#contact' text='Message' style='w-full sm:w-1/2 lg:w-full bg-primary-01  py-2 px-4 lg:py-4 lg:px-8 font-bold' textColor='xl:text-2xl text-secondary-01' />
      
      <div className="pt-6 border-t border-gray-200">
        <div className="flex flex-wrap items-baseline gap-x-2 gap-y-1 mb-4">
          <h3 className="font-bold text-lg xl:text-2xl text-primary-01">Categories :</h3>
          {categories.map((cat, index) => (
            <a key={index} href={cat.url} className="text-sm  xl:text-xl text-[#191300] hover:text-primary-01 underline">
              {cat.name}{index < categories.length - 1 ? ',' : ''}
            </a>
          ))}
        </div>
        <div className="flex flex-wrap items-baseline gap-2">
          <h3 className="font-bold text-lg xl:text-2xl text-primary-01">Tag :</h3>
          {tags.map((tag, index) => (
            <a key={index} href={tag.url} className="text-sm  xl:text-xl text-[#191300] hover:text-primary-01 underline">
              {tag.name}
            </a>
          ))}
        </div>
      </div>
    </div>
  );
};