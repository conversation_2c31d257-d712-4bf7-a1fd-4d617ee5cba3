import { useParams } from 'react-router-dom';
import CommonHero from '../../../sections/CommonHero';
import ProductsSection from '../components/CategoryProductsSection';
import yellowLineImage from '../../../assets/images/aboutPage/banner.png';

import { useTypesApi } from '../../../services/api/use-type-api';

const CategoryPage = () => {
  const { id } = useParams<{ id: string }>();
  const categoryId = id ? parseInt(id, 10) : 0;

  const { useGetSubTypeDetails } = useTypesApi();
  const { data: apiResponse, isLoading, isError, error } = useGetSubTypeDetails(categoryId);

  if (isLoading) {
    return <div className="text-center p-24 h-screen text-primary-01 text-xl ">Loading Category...</div>;
  }

  if (isError || !apiResponse?.data) {
    return (
      <div className="text-center p-24 h-screen text-red-500 text-xl ">
        <h2>Error loading category.</h2>
        <p>{error?.message || 'The category could not be found.'}</p>
      </div>
    );
  }

  const categoryData = apiResponse.data;

  const heroImageUrl = categoryData.media?.[0]?.url || ''; 

  return (
    <div>
      <CommonHero 
        image={heroImageUrl} 
        yellowLineImage={yellowLineImage}
        title={categoryData.name}
        description={categoryData.description}
        subHero={true}
        isCenter={true}
      />
      <ProductsSection products={categoryData.products} />
    </div>
  );
}

export default CategoryPage;