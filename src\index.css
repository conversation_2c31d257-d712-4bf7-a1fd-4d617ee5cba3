
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');

@import "tailwindcss";
/* Define the DIN Next LT Arabic font family with all its weights */
@font-face {
  font-family: 'DIN Next LT Arabic';
  src: url('./assets/fonts/DINNextLTArabic-UltraLight.ttf') format('truetype');
  font-weight: 200; /* UltraLight */
  font-style: normal;
  font-display:swap;
}
@font-face {
  font-family: 'DIN Next LT Arabic';
  src: url('./assets/fonts/DINNextLTArabic-Light.ttf') format('truetype');
  font-weight: 300; /* Light */
  font-style: normal;
  font-display:swap;
}
@font-face {
  font-family: 'DIN Next LT Arabic';
  src: url('./assets/fonts/DINNextLTArabic-Regular.ttf') format('truetype');
  font-weight: 400; /* Regular */
  font-style: normal;
  font-display:swap;
}
@font-face {
  font-family: 'DIN Next LT Arabic';
  src: url('./assets/fonts/DINNextLTArabic-Medium.ttf') format('truetype');
  font-weight: 500; /* Medium */
  font-style: normal;
  font-display:swap;
  font-display:swap;
}
@font-face {
  font-family: 'DIN Next LT Arabic';
  src: url('./assets/fonts/DINNextLTArabic-Bold.ttf') format('truetype');
  font-weight: 700; /* Bold */
  font-style: normal;
  font-display:swap;
}
@font-face {
  font-family: 'DIN Next LT Arabic';
  src: url('./assets/fonts/DINNextLTArabic-Heavy.ttf') format('truetype');
  font-weight: 800; /* Heavy */
  font-style: normal;
  font-display:swap;
}
@font-face {
  font-family: 'DIN Next LT Arabic';
  src: url('./assets/fonts/DINNextLTArabic-Black.ttf') format('truetype');
  font-weight: 900; /* Black */
  font-style: normal;
  font-display:swap;
}

@font-face {
  font-family: 'NextStep';
  src: url('./assets/fonts/NEXTSTEP.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display:swap;
}
@font-face {
  font-family: 'NextStep';
  src: url('./assets/fonts/NEXTSTEP.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display:swap;
}
@font-face {
  font-family: 'Ink Brush Arabic';
  src: url('./assets/fonts/InkBrushArabic_DEMO-Textured.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
  font-display:swap;
}
@theme {
  --font-nextstep: 'NextStep', sans-serif;
  --font-inkbrush: 'Ink Brush Arabic', sans-serif;
  --color-primary-01:#FCC50D;
  --color-primary-02: #E2B11E; /* darkColor */

  --color-secondary-01:#3C3C3B;
  --color-secondary-02: #3C3C3BE5; /* 90% */
  --color-secondary-03:#3C3C3BCC; /* 80% */

  --color-white-01: #FFFEFA;
  --color-white-02: #F6F6F6;
  --color-gray:#8B887E;

  --spacing-main-padding-large: 100px;
}
@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none; 
    scrollbar-width: none;
  }
}
html {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: #FCC50D #3C3C3B;
  font-family: 'DIN Next LT Arabic', sans-serif;
}
/* scroll page */
html::-webkit-scrollbar { 
  width: 12px;
}
html::-webkit-scrollbar-track {
  background: #3C3C3B;
}
html::-webkit-scrollbar-thumb {
  background-color: #FCC50D;
  border-radius: 20px;
}

html::-webkit-scrollbar-thumb:hover {
  background-color: #fcc40dc4; 
}



@keyframes wiggle-arrow {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(2px);
  }
  75% {
    transform: translateX(-2px);
  }
}
.animate-wiggle {
  animation: wiggle-arrow 1s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(-4px);
  }
  50% {
    transform: translateY(4px);
  }
}

.floating-light {
  animation: floating 3s ease-in-out infinite;
}

.floating-medium {
  animation: floating 4s ease-in-out infinite;
}

/* start ProductsMegaMenu */
.mega-menu-column-split {
  column-count: 2;
  column-gap: 10px; 
}

.mega-menu-column-split > * {
  break-inside: avoid;
  margin-bottom: 16px; 
}
.custom-scrollbar::-webkit-scrollbar {
  width: 12px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2); 
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #ffc423; 
  border-radius: 10px;
  border: 3px solid transparent;
  background-clip: content-box;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #ffda73;
}
.custom-scrollbar {
  scrollbar-width: auto;
  scrollbar-color: #ffc423 rgba(0, 0, 0, 0.2);
}
/* end ProductsMegaMenu */

@keyframes crossFadeZoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1); 
  }
}

.animate-crossFadeZoomIn {
  animation: crossFadeZoomIn 0.5s ease-in-out;
}