import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import MainPage from "./pages/main-page/view/main-page";
import Layout from "./layout/layout";
import AboutPage from "./pages/AboutPage/view/AboutPage";
import 'aos/dist/aos.css'
import AOS from 'aos'
import { useEffect, useState } from "react";
import Preloader from './components/Preloader'
import ScrollToTop from "./components/ScrollToTop";
import PortfolioPage from "./pages/PortfolioPage/view/PortfolioPage";
import CategoryPage from "./pages/CategoryPage/view/CategoryPage";
import ProductDetailPage from "./pages/ProductPage/view/ProductDetailPage";
import CSRAndCGPage from "./pages/CSRAndCGPage/view/CSRAndCGPage";
import OurCommitmentPage from "./pages/OurCommitmentPage/view/OurCommitmentPage";
import LicencesPage from "./pages/LicencesPage/view/LicencesPage";
import ComingSoonPage from "./pages/ComingSoonPage/view/ComingSoonPage";

function App() {

  const [showPreloader, setShowPreloader] = useState(true)
  
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }, [])

  useEffect(() => {

    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: false,
      mirror: false
    })

    const timer = setTimeout(() => {
      setShowPreloader(false)
    }, 3000)

    return () => clearTimeout(timer)

  }, [])

  return (
    <>
    {showPreloader ? <Preloader /> : null}
    {!showPreloader && (
      <>
      <ScrollToTop/>
     <Router>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route  index element={<MainPage />} />
            <Route path="/who-we-are" element={<AboutPage/>}/> 
            <Route path="/csr-and-cg" element={<CSRAndCGPage/>}/>
            <Route path="/our-commitment-to-safety" element={<OurCommitmentPage/>}/>
            <Route path="/licences-and-certifications" element={<LicencesPage/>}/>
            <Route path="/portfolio" element={<PortfolioPage/>}/> 
            <Route path="/category/:id" element={<CategoryPage/>}/>
            <Route path="/products/:id" element={<ProductDetailPage/>}/>
        </Route>
        <Route path="*" element={<ComingSoonPage />} />
      </Routes>
    </Router>
    </>
    )}
    </>
  );
}

export default App;
