import { useState, useEffect } from "react";
import { NavLink } from "react-router-dom";
import { type MegaMenuColumn } from "../data/NavLinkData";

const ProductsMegaMenu: React.FC<{
  columns: MegaMenuColumn[];
  onLinkClick: () => void;
}> = ({ columns, onLinkClick }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [expandedLists, setExpandedLists] = useState<{
    [key: string]: boolean;
  }>({});
  const initialDisplayCount = 2;

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleToggleExpand = (listId: string) => {
    setExpandedLists((prev) => ({ ...prev, [listId]: !prev[listId] }));
  };

  return (
    <div
      className={`
        absolute top-full left-0 w-full bg-secondary-02 z-50  max-h-[calc(100vh-100px)] custom-scrollbar
        border-b-[32px] border-primary-01 rounded-b-4xl  flex flex-col
        transition-all duration-300 ease-in-out
        ${
          isVisible
            ? "opacity-100 translate-y-0"
            : "opacity-0 -translate-y-4 pointer-events-none"
        }
      `}
    >
      <div className="max-w-[1536px] mx-auto pb-4 px-5 md:px-[60px] xl:px-main-padding-large overflow-y-auto  ">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-x-10">
          {columns.map((column, index) => (
            <div key={index}>
              <h3 className="text-xl font-bold text-white py-5 border-b border-white mb-4">
                {column.title}
              </h3>

              <div
                className={
                  index === 1 || index === 2
                    ? "mega-menu-column-split"
                    : "flex flex-col gap-y-4"
                }
              >
                {column.subgroups.map((subgroup) => {
                  const listId = `${column.title}-${subgroup.title}`;
                  const isExpandable =
                    subgroup.links.length > initialDisplayCount;
                  const isExpanded = !!expandedLists[listId];
                  const linksToShow =
                    isExpandable && !isExpanded
                      ? subgroup.links.slice(0, initialDisplayCount)
                      : subgroup.links;

                  return (
                    <div key={subgroup.id}>
                      {subgroup.path ? (
                        <NavLink
                          to={subgroup.path}
                          onClick={onLinkClick}
                          className="text-lg font-bold text-primary-01 block hover:text-white transition-colors"
                        >
                          {subgroup.title}
                        </NavLink>
                      ) : (
                        <h4 className="text-lg font-bold text-primary-01 mb-3">
                          {subgroup.title}
                        </h4>
                      )}

                      {subgroup.links.length > 0 && (
                        <ul className="flex flex-col gap-2">
                          {linksToShow.map((productLink) => (
                            <li key={productLink.id}>
                              <NavLink
                                to={productLink.path}
                                onClick={onLinkClick}
                                className="text-white hover:text-primary-01 text-base transition-colors"
                              >
                                {productLink.text}
                              </NavLink>
                            </li>
                          ))}
                        </ul>
                      )}

                      {isExpandable && (
                        <button
                          onClick={() => handleToggleExpand(listId)}
                          className="text-primary-01 text-sm mt-3 hover:underline focus:outline-none"
                        >
                          {isExpanded ? "See Less" : "See All"}
                        </button>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProductsMegaMenu;
