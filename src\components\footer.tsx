import LogoImg from "../assets/images/navbar/logo-haz.svg";
import { Link } from "react-router-dom";
import { navigationLinks, socialLinks } from "../data/FooterData";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const Footer: React.FC = () => {
  return (
    <footer className=" bg-secondary-01 text-white pb-5">
      <div className="p-5 md:p-[60px] xl:p-main-padding-large flex flex-col gap-5 md:gap-10 xl:gap-[72px] 2xl:container mx-auto">
        <div className="flex max-[800px]:flex-col items-center justify-between flex-row gap-4">
          <Link to="/">
            <img src={LogoImg} alt="logo" />
          </Link>
          <ul className="flex flex-wrap justify-center gap-5 sm:gap-5 lg:gap-10 mt-5 md:mt-0">
            {navigationLinks.map((link, index) => (
              <li
                key={index}
                className="text-white transition-all ease-in-out duration-300 hover:text-primary-01 scale-100 hover:scale-105"
              >
                <Link to={link.href}>{link.name}</Link>
              </li>
            ))}
          </ul>
        </div>
        <div className="flex flex-col items-center justify-between gap-6 lg:flex-row ">
          <p className="max-lg:text-sm text-center">
            Thank you for your partnership. Let's shape the future of
            excellence, reliability, and speed.
            <span className="block text-center text-2xl md:text-3xl font-inkbrush mt-2">
              Seamlessly yours
            </span>
          </p>
          <div className="flex items-center gap-3 sm:gap-6">
            {socialLinks.map((link, index) => (
              <div
                key={index}
                className="text-white flex flex-col gap-1 items-center justify-center"
              >
                <span className="text-lg md:text-2xl">
                  <FontAwesomeIcon icon={link.icon} />
                </span>
                <span className="max-[330px]:text-[10px] text-xs">
                  {link.text}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
