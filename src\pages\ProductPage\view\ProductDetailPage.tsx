import { useMemo } from "react";
import { useParams } from "react-router-dom";
import { useProductsApi } from "../../../services/api/use-product-api";
import { Breadcrumbs } from "../components/Breadcrumbs";
import { ProductInfo } from "../components/ProductInfo";
import { ImageGallery } from "../components/ImageGallery";
import {
  ProductInfoSection,
  type ProductInfoTab,
} from "../components/ProductInfoSection/ProductInfoSection";
import ProductsSlider from "../components/ProductsSlider";
import { categoryExternalLinks } from "../../../data/categoryExternalLinks";

const ProductDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const productId = id ? parseInt(id, 10) : 0;

  const { useGetProduct, useGetProducts } = useProductsApi();
  const {
    data: productResponse,
    isLoading: isLoadingProduct,
    isError: isProductError,
  } = useGetProduct(productId);
  const { data: allProductsResponse, isLoading: isLoadingList } =
    useGetProducts();

  const product = productResponse?.data;

  const imageGalleryUrls = useMemo(
    () => product?.images.map((img) => img.url) ?? [],
    [product]
  );

  const productCategories = useMemo(() => {
    if (!product?.categories) {
      return [];
    }

    return product.categories.split(",").map((cat) => {
      const categoryName = cat.trim();
      const finalUrl = categoryExternalLinks[categoryName] || "#";

      return {
        name: categoryName,
        url: finalUrl,
      };
    });
  }, [product]);

  const productTags = useMemo(
    () =>
      product?.tags.split(",").map((tag) => ({ name: tag.trim(), url: "#" })) ??
      [],
    [product]
  );

  const productInfoTabs = useMemo((): ProductInfoTab[] => {
    if (!product) return [];
    return [
      {
        id: "specification",
        title: "Specification",
        content: product.specifications,
        type: "list",
      },
      {
        id: "keyBenefits",
        title: "Key Benefits",
        content: product.key_benefits,
        type: "mediaList",
      },
      {
        id: "applications",
        title: "Applications",
        content: product.applications,
        type: "mediaList",
      },
    ];
  }, [product]);

  const relatedProducts = useMemo(() => {
    if (!product || !allProductsResponse?.data || !product.type?.id) {
      return [];
    }

    return allProductsResponse.data.filter((p) => {
      const isNotSameProduct = p.id !== product.id;
      const isSameType = p.type?.id === product.type.id;

      return isNotSameProduct && isSameType;
    });
  }, [allProductsResponse, product]);

  if (isLoadingProduct || isLoadingList) {
    return (
      <div className="text-center p-24 h-screen text-primary-01 text-xl ">
        Loading Product...
      </div>
    );
  }

  if (isProductError || !product) {
    return (
      <div className="text-center p-24 h-screen text-red-500 text-xl">
        Error: Product not found.
      </div>
    );
  }

  return (
    <section className="bg-white py-12 md:py-20">
      <div className="2xl:container mx-auto px-5 md:px-[60px] xl:px-main-padding-large pt-10 md:pt-4">
        <Breadcrumbs
          crumbs={[
            { name: "Home", url: "/" },
            { name: "Products" },
            { name: product.name, url: `/products/${product.id}` },
          ]}
        />
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start pt-5 md:pt-[60px] xl:pt-main-padding-large pb-[60px]">
          <ProductInfo
            name={product.name}
            description={product.description}
            categories={productCategories}
            tags={productTags}
          />
          <ImageGallery images={imageGalleryUrls} productName={product.name} />
        </div>
      </div>

      <ProductInfoSection
        productTabs={productInfoTabs}
        promoImages={product.images}
        videoUrl={product.video}
      />
      <ProductsSlider products={relatedProducts} />
    </section>
  );
};

export default ProductDetailPage;
