import type { ProductForMenu } from '../../../services/api/use-type-api'; 
import CommonButton from '../../../components/CommonButton';

interface ProductCardProps {
  product: ProductForMenu;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {

  const productImageUrl = product.images?.[0]?.url || '';

  return (
    <div 
      data-aos="fade-up" 
      data-aos-duration="2000" 
      className="bg-white rounded-[28px] shadow-[6px_8px_18.6px_0px_#3C3C3B1A] overflow-hidden p-4 
                 flex flex-col text-center items-center gap-4  h-full"    
    >
      
      <div className='w-full h-64 bg-gray-50 rounded-xl'>
        <img 
          src={productImageUrl} 
          alt={product.name}
          className="w-full h-full object-contain"
          loading="lazy"
        />
      </div>
      
      <div className="flex-grow w-full">
        <h3 className="text-lg xl:text-2xl font-bold text-primary-01 mb-1 px-4">
          {product.name}
        </h3>
        <p className="text-secondary-01 text-base xl:text-xl  px-3">
          {product.mini_description}
        </p>
      </div>
      
      <div className="mt-auto">
        <CommonButton 
          path={`/products/${product.id}`} 
          text='Learn More' 
          style='bg-secondary-01 px-4 py-2 xl:px-8 xl:py-4 text-sm xl:text-base' 
          textColor='text-white'
        />
      </div>
    </div>
  );
};

export default ProductCard;