import type { ValueCardData } from "../../../../data/CoreValueData";

export const ValueCard: React.FC<ValueCardData> = ({
  title,
  quote,
  author,
  tagTheme,
}) => {
  const themeStyles = {
    yellow: {
      titleColor: "text-primary-01",
      tagBg: "bg-primary-01",
    },
    dark: {
      titleColor: "text-secondary-01",
      tagBg: "bg-secondary-01",
    },
  };

  const currentTheme = themeStyles[tagTheme];

  return (
    <div
      data-aos="fade-up"
      className="relative bg-[#F8F9FA]/70 rounded-2xl shadow-md overflow-hidden min-h-[160px] 
    flex flex-col justify-center transition-transform duration-300 hover:scale-105"
    >
      <div className="p-6 pb-14">
        <h3
          className={`${currentTheme.titleColor} font-bold text-xl uppercase tracking-wide`}
        >
          {title}
        </h3>
        <p className="text-gray-500 mt-2 italic text-base">"{quote}"</p>
      </div>

      <div
        className={`absolute bottom-0 text-center right-0 w-[190px] py-2 ${currentTheme.tagBg} rounded-tl-2xl`}
      >
        <span className="text-white text-sm font-bold uppercase tracking-wider">
          {author}
        </span>
      </div>
    </div>
  );
};
