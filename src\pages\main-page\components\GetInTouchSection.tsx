import { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCaretDown } from "@fortawesome/free-solid-svg-icons";
import bannerImg from "../../../assets/images/mainPage/banner.svg";
import bgImg from "../../../assets/images/mainPage/GetInTouch-bg.webp";
import SectionTitle from "../../../components/SectionTitle";
import { formFields, contactInfo } from "../../../data/GetInTouchData";
import { useUserMessageApi } from "../../../services/api/use-user-message-api";
import type { CreateUserMessageRequest } from "../../../services/api/use-user-message-api";

const initialFormState: CreateUserMessageRequest = {
  name: "",
  phone: "",
  email: "",
  message: "",
  finding_way: "",
};

const GetInTouchSection: React.FC = () => {
  const [formData, setFormData] =
    useState<CreateUserMessageRequest>(initialFormState);

  const { useCreateUserMessage } = useUserMessageApi();

  const mutation = useCreateUserMessage(() => {
    setFormData(initialFormState);
  });

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    mutation.mutate(formData);
    console.log(formData);
  };

  return (
    <section
      id="contact"
      className=" bg-white-02 2xl:container mx-auto flex items-center justify-between overflow-hidden "
    >
      <div className="relative grid grid-cols-1 lg:grid-cols-2  ">
        <div className=" order-2 lg:order-1 p-5 lg:p-[60px] xl:p-main-padding-large lg:pr-[67px] ">
          <SectionTitle
            title="Get In Touch"
            description="Let's build the future of industrial automation together. Contact us today to explore how we can optimize your operations!"
            descStyle="text-base mt-5 mb-10 "
          />
          <form
            onSubmit={handleSubmit}
            className="space-y-5"
            data-aos="fade-right"
            data-aos-offset="300"
            data-aos-easing="ease-in-sine"
          >
            {formFields.map((field, index) => (
              <div key={index}>
                <label htmlFor={field.id} className="sr-only">
                  {field.placeholder}
                </label>
                {field.type === "select" ? (
                  <div className="relative">
                    <select
                      id={field.id}
                      name={field.name}
                      value={
                        formData[field.name as keyof CreateUserMessageRequest]
                      }
                      onChange={handleChange}
                      required={field.required}
                      className="appearance-none w-full px-5 py-3 bg-white-01 rounded-[27px] text-[#828282] border border-[#E0E0E0] focus:outline-none focus:ring-2 focus:ring-primary-01 transition"
                    >
                      <option value="How did you find us?">
                        {field.placeholder}
                      </option>
                      {field.options?.map((option, index) => (
                        <option key={index} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-4 text-secondary-01">
                      <FontAwesomeIcon icon={faCaretDown} />
                    </div>
                  </div>
                ) : field.type === "textarea" ? (
                  <textarea
                    id={field.id}
                    name={field.name}
                    value={
                      formData[field.name as keyof CreateUserMessageRequest]
                    }
                    onChange={handleChange}
                    placeholder={field.placeholder}
                    required={field.required}
                    rows={5}
                    className="w-full px-5 py-3 resize-none caret-primary-01 bg-white-01 rounded-[27px] text-[#828282] border border-[#E0E0E0] focus:outline-none focus:ring-2 focus:ring-primary-01 transition"
                  />
                ) : (
                  <input
                    type={field.type}
                    id={field.id}
                    name={field.name}
                    value={
                      formData[field.name as keyof CreateUserMessageRequest]
                    }
                    onChange={handleChange}
                    placeholder={field.placeholder}
                    required={field.required}
                    className="w-full px-5 py-3 caret-primary-01 bg-white-01 rounded-[27px] text-[#828282] border border-[#E0E0E0] focus:outline-none focus:ring-2 focus:ring-primary-01 transition"
                  />
                )}
              </div>
            ))}
            <button
              type="submit"
              disabled={mutation.isPending}
              className="block w-fit px-10 mx-auto lg:w-full py-3 bg-primary-01 rounded-[27px] text-secondary-01 text-base font-bold uppercase cursor-pointer  disabled:bg-primary-01/75 disabled:cursor-not-allowed transition-colors duration-300"
            >
              {mutation.isPending ? "SENDING..." : "SEND"}
            </button>
          </form>
          <div className="mt-[60px] flex flex-wrap items-center justify-center gap-5 xl:gap-[50px] ">
            {contactInfo.map((info, index) => (
              <div
                key={index}
                className="flex flex-col xl:flex-row text-center xl:text-start gap-4 items-center text-secondary-01 "
              >
                <FontAwesomeIcon icon={info.icon} className="scale-150" />
                <div>
                  <h3 className="font-bold   text-[13px] ">{info.title}</h3>
                  <p className=" text-[13px]">{info.details}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div
          className="hidden lg:block relative order-1 lg:order-2 bg-no-repeat bg-cover"
          style={{ backgroundImage: `url(${bannerImg})` }}
          data-aos="fade-left"
          data-aos-offset="300"
          data-aos-easing="ease-in-sine"
        >
          <img
            src={bgImg}
            alt="background image"
            className="absolute bottom-0 right-0 z-10"
            loading="lazy"
          />
        </div>
      </div>
    </section>
  );
};

export default GetInTouchSection;
