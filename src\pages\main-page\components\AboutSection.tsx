import AboutImg from "../../../assets/images/mainPage/about-img.webp";
import { circlesData } from "../../../data/AboutSectionData";
import InfoSection from "../../../sections/InfoSection";
import useMediaQuery from "../../../components/useMediaQuery";

function AboutSection() {
  const isSpecificMedium = useMediaQuery(
    "(min-width: 1260px) and (max-width: 1450px) "
  );
  const width1450 = useMediaQuery("(min-width: 1450px) ");

  const targetCircle = circlesData.find((circle) => circle.id === 2);
  if (targetCircle) {
    if (isSpecificMedium) {
      targetCircle.positionClasses = "left-1/4 bottom-[20%]";
    } else if (width1450) {
      targetCircle.positionClasses = "left-[23%] bottom-[20%]";
    }
  }

  return (
    <section className="2xl:container mx-auto p-5 md:p-[60px] xl:p-main-padding-large overflow-hidden">
      <InfoSection
        leftContent={AboutImg}
        title="Who We Are"
        info="HAZ Operations Trading LLC is a fast-growing provider of industrial automation and material handling solutions, helping businesses optimize operations, reduce costs, and transition to Industry 4.0 & Beyond."
        buttonText="Read More"
        circles={circlesData}
      />
    </section>
  );
}

export default AboutSection;
