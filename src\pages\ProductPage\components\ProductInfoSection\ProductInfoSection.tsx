import { useState, useEffect } from 'react';
import { faCubes, faLightbulb, faListAlt, faInfoCircle } from '@fortawesome/free-solid-svg-icons';
import Tabs from './Tabs';
import { TabContent } from './TabContent';
import type { ProductApplication, ProductKeyBenefit, ProductSpecification, ProductMedia } from '../../../../services/api/use-product-api';

export interface ProductInfoTab {
  id: string;
  title: string;
  content: string | ProductSpecification[] | ProductKeyBenefit[] | ProductApplication[];
  type: 'text' | 'list' | 'mediaList';
}

const ICON_MAP = {
  description: faInfoCircle,
  specification: faListAlt,
  keyBenefits: faLightbulb,
  applications: faCubes,
};

interface ProductInfoSectionProps {
  productTabs: ProductInfoTab[];
  promoImages: ProductMedia[];
  videoUrl: string;
}

export const ProductInfoSection: React.FC<ProductInfoSectionProps> = ({ productTabs, promoImages, videoUrl }) => {
  
  const [activeTabId, setActiveTabId] = useState<string | undefined>();
  
  // Filter the tabs to create the list of what's available.
  const availableTabs = productTabs.filter(tab => {
    return tab.content && tab.content.length > 0;
  });

  useEffect(() => {
    if (availableTabs.length > 0 && !activeTabId) {
      setActiveTabId(availableTabs[0].id);
    }
  }, [availableTabs, activeTabId]);

  // The early return now comes AFTER all hooks have been called.
  if (availableTabs.length === 0) {
    return null;
  }

  const activeTab = availableTabs.find(tab => tab.id === activeTabId);

  return (
    <div className="w-full 2xl:container mx-auto px-5 md:px-[60px] xl:px-main-padding-large pb-20">
      <Tabs
        tabs={availableTabs}
        activeTabId={activeTabId}
        onTabClick={setActiveTabId}
        iconMap={ICON_MAP}
      />
      <TabContent 
        activeTab={activeTab} 
        promoImages={promoImages} 
        videoUrl={videoUrl} 
      />
    </div>
  );
};