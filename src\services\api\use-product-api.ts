import useApiServices from "../hooks/use-api-services";
import axiosInstance from "../../utils/axios";

export interface ProductMedia {
  id: number;
  url: string;
  type: string;
}

export interface ProductCategory {
  id: number;
  name: string;
}

export interface ProductApplication {
  id: number;
  title: string;
  media: ProductMedia[];
}

export interface ProductKeyBenefit {
  id: number;
  title: string;
  media: ProductMedia;
}

export interface ProductSpecification {
  id: number;
  title: string;
  content: string;
}

export interface Product {
  id: number;
  type: ProductCategory;
  name: string;
  slug: string;
  is_home:boolean;
  description: string;
  mini_description: string;
  categories: string; 
  tags: string;      
  images: ProductMedia[];
  applications: ProductApplication[];
  key_benefits: ProductKeyBenefit[];
  specifications: ProductSpecification[];
  video: string;
}

export interface ProductsListResponse {
    status: string;
    message: string;
    data: Product[];
}

export interface ProductResponse {
    status: string;
    data: Product;
    message: string;
}

export const ProductEndpoints = {
  list: "/products",
  details: "/products",
};

export const useProductsApi = () => {
  const { useGetListService, useGetItemService } = useApiServices({ axiosInstance });

  // fetch a LIST of products
  const useGetProducts = (options?: { params?: { per_page: number } }) => {
    return useGetListService<ProductsListResponse, { per_page: number }>({
      url: ProductEndpoints.list,
      params: options?.params,
    });
  };
  // fetch a SINGLE product
  const useGetProduct = (id: number) => {
    return useGetItemService<ProductResponse>({
      url: ProductEndpoints.details,
      id: id.toString(),
    });
  };

  return {
    useGetProducts,
    useGetProduct,
  };
};