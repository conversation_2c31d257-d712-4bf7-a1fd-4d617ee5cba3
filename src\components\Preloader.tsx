
const animationStyles = `
  @keyframes clockwise {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  @keyframes counter-clockwise {
    from { transform: rotate(0deg); }
    to { transform: rotate(-360deg); }
  }
`;

interface GearProps {
  isLarge?: boolean;
  className?: string;
  animationClass: string;
}

const Gear: React.FC<GearProps> = ({ isLarge = false, className = '', animationClass }) => {
  const bars = isLarge
    ? ['rotate-0', 'rotate-60', 'rotate-120', 'rotate-90', 'rotate-30', 'rotate-150']
    : ['rotate-0', 'rotate-60', 'rotate-120'];

  const gearSize = isLarge ? 'h-[120px] w-[120px]' : 'h-[60px] w-[60px]';
  const afterPseudo = isLarge
    ? "after:h-[96px] after:w-[96px] after:-ml-[48px] after:-mt-[48px]"
    : "after:h-[36px] after:w-[36px] after:-ml-[18px] after:-mt-[18px]";
  
  const barSize = isLarge ? 'w-[136px] -ml-[68px]' : 'w-[76px] -ml-[38px]';
  const yellowColor = 'bg-white'; 
  const highlightShadow = 'shadow-[0px_-1px_0px_0px_rgba(255,255,255,0.4),0px_1px_0px_0px_rgba(0,0,0,0.2)]';
  const innerHoleBg = 'after:bg-primary-01';
  const innerHoleShadow = 'after:shadow-[inset_0_0_6px_rgba(0,0,0,0.3)]';

  return (
    <div
      className={`gear absolute rounded-full ${gearSize} ${className}
                 ${highlightShadow}
                 after:content-[''] after:absolute after:rounded-full after:top-1/2 after:left-1/2 after:z-[3]
                 ${innerHoleBg}
                 ${innerHoleShadow}
                 ${afterPseudo}`}
    >
      <div className={`gear-inner relative h-full w-full ${yellowColor} rounded-full border border-secondary-01 ${animationClass}`}>
        {bars.map((rotation, index) => (
          <div
            key={index}
            className={`bar absolute ${yellowColor} h-[16px] left-1/2 top-1/2 -mt-[8px] rounded-[2px] 
                       border-l border-r border-secondary-01
                       ${barSize} ${rotation}`}
          ></div>
        ))}
      </div>
    </div>
  );
};

const Preloader: React.FC = () => {
  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-primary-01">     
      <style>{animationStyles}</style>
      <div className="gearbox relative h-[150px] w-[200px]">    
        <Gear 
          className="top-[12px] left-[10px]"
          animationClass="animate-[counter-clockwise_3s_linear_infinite]"
        />  
        <Gear 
          className="top-[61px] left-[60px]"
          animationClass="animate-[clockwise_3s_linear_infinite]"
        />
        <Gear 
          className="top-[110px] left-[10px]"
          animationClass="animate-[counter-clockwise_3s_linear_infinite]"
        />
        <Gear
          isLarge={true}
          className="top-[13px] left-[128px]"
          animationClass="animate-[counter-clockwise_6s_linear_infinite]"
        />
      </div>
    </div>
  );
};

export default Preloader;