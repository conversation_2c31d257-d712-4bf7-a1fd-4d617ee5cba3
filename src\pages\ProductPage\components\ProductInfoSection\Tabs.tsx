
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { type IconDefinition } from '@fortawesome/fontawesome-svg-core';
import completeBg from '../../../../assets/images/productDetailsPage/bg-tabs.png';
import { useDragToScroll } from './useDragToScroll';
import type { ProductInfoTab } from './ProductInfoSection';

type TabId = ProductInfoTab['id'];
interface IconMap {
  [key: string]: IconDefinition;
}

interface TabsProps {
  tabs: ProductInfoTab[];
  activeTabId?: TabId;
  onTabClick: (tabId: TabId) => void;
  iconMap: IconMap;
}

const Tabs: React.FC<TabsProps> = ({ tabs, activeTabId, onTabClick, iconMap }) => {
    const scrollRef = useDragToScroll();

  return (
    <div 
      className={`relative flex justify-center items-center p-4 bg-cover bg-repeat-x`}
      style={{ backgroundImage: `url(${completeBg})`}} >
      <div 
        ref={scrollRef} 
        className="relative flex items-center gap-4 w-full overflow-x-auto flex-nowrap no-scrollbar  cursor-grab active:cursor-grabbing"
      >
        {tabs.map((tab) => {
          const iconDefinition = iconMap[tab.id];
          const isActive = activeTabId === tab.id;
          
          return (
            <div key={tab.id} className="flex-shrink-0">
              <button
                onClick={() => onTabClick(tab.id)}
                className={`flex flex-col items-center justify-center rounded-full w-[120px] h-[120px] md:w-36 md:h-36 lg:w-[157px] lg:h-[157px] cursor-pointer
                  transition-all duration-300 transform hover:bg-primary-01 border-[3px] border-primary-01 select-none ${
                    isActive ? 'bg-primary-01' : 'bg-white'
                  }`}
              >
                {iconDefinition && (
                  <FontAwesomeIcon
                    icon={iconDefinition}
                    className={`text-3xl lg:text-5xl mb-1 transition-colors duration-300 pointer-events-none ${
                      isActive ? 'text-white' : 'text-secondary-01'
                    }`}
                  />
                )}
                <span className={`md:text-lg lg:text-xl font-bold transition-colors duration-300 pointer-events-none ${
                    isActive ? 'text-white' : 'text-secondary-01'
                }`}>
                  {tab.title}
                </span>
              </button>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Tabs;