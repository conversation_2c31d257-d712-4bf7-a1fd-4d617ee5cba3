import ServiceCard from "./ServiceCard";
import backgroundImage from "../../../../assets/images/mainPage/services.webp";
import gridPatternImage from "../../../../assets/images/mainPage/services-vector.svg";
import SectionTitle from "../../../../components/SectionTitle";
import { servicesData } from "../../../../data/ServicesData";

const OurServicesSection = () => {
  return (
    <section
      id="services"
      className="relative py-5 md:py-[60px] xl:py-main-padding-large overflow-hidden"
    >
      <div
        className="absolute inset-0 bg-cover bg-center -z-30"
        style={{ backgroundImage: `url(${backgroundImage})` }}
      ></div>

      <div className="absolute inset-0 bg-black/80 -z-20"></div>
      <div
        className="absolute inset-0 bg-repeat bg-center -z-10"
        style={{ backgroundImage: `url(${gridPatternImage})` }}
      ></div>

      <SectionTitle
        title="Our Services"
        className="items-center"
        textColor="text-white"
      />
      <div
        className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 mt-12 2xl:container mx-auto
         px-5 md:px-[60px] xl:px-main-padding-large"
      >
        {servicesData.map((service, index) => (
          <ServiceCard
            key={index}
            icon={service.icon}
            title={service.title}
            description={service.description}
            hoverImage={service.hoverImage}
            hoverText={service.hoverText}
          />
        ))}
      </div>
    </section>
  );
};

export default OurServicesSection;
