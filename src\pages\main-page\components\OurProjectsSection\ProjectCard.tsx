import CommonButton from "../../../../components/CommonButton";
import type { ProjectCardData } from "../../../../data/ProjectsData";
import React, { useState } from "react";

interface ProjectCardProps {
  card: ProjectCardData;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ card }) => {
  const {
    type,
    title,
    description,
    buttonText,
    imageUrl,
    altText,
    gridClasses,
    bgColor,
    hoverText,
    path,
  } = card;

  const [isClicked, setIsClicked] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const isActive = isClicked || isHovered;

  if (type === "image") {
    return (
      <div
        data-aos="zoom-in"
        className={`${gridClasses} overflow-hidden relative cursor-pointer`}
        onClick={() => setIsClicked((prev) => !prev)}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <img
          src={imageUrl}
          alt={altText || "Project Image"}
          className={`w-full h-full object-cover transition-transform duration-300 ${
            isActive ? "scale-105" : "scale-100"
          }`}
          loading="lazy"
        />
        {hoverText && (
          <div
            className={`absolute inset-x-0 bottom-0 h-2/3 bg-gradient-to-t from-black/90 via-black/70 to-transparent 
                        flex flex-col items-center justify-end px-4 pb-5 text-start
                        transition-all duration-300 ease-in-out
                        ${
                          isActive
                            ? "opacity-100 translate-y-0"
                            : "opacity-0 translate-y-10"
                        }`}
          >
            <p className="text-white font-semibold md:text-lg">{hoverText}</p>
          </div>
        )}
      </div>
    );
  }

  return (
    <div
      data-aos="zoom-in"
      className={`${gridClasses} ${bgColor} text-white flex flex-col justify-center items-center p-4`}
    >
      <div
        className={`${title == "Our Projects" ? "text-start" : "text-center"} `}
      >
        <h3
          className={`font-bold text-lg sm:text-xl xl:text-2xl mb-2 ${
            bgColor === "bg-primary-01" ? "text-secondary-01" : "text-white"
          }`}
        >
          {title}
        </h3>
        {description && (
          <p className="mb-6 text-base text-white-01">{description}</p>
        )}
        {buttonText && (
          <CommonButton
            path={path}
            text={buttonText}
            style="text-base px-3 xl:px-6 py-2 xl:py-3 w-fit mx-auto"
            textColor={` ${
              bgColor === "bg-primary-01"
                ? "bg-secondary-01 text-white"
                : "bg-primary-01 text-secondary-01"
            }`}
          />
        )}
      </div>
    </div>
  );
};

export default ProjectCard;
