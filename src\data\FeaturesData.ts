import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faAward, faLightbulb, faUsersCog, } from "@fortawesome/free-solid-svg-icons";

interface FeatureData {
  icon: IconDefinition;
  title: string;
  description: string;
  textPosition: string;
}

export const featuresData: FeatureData[] = [
  {
    icon: faUsersCog,
    title: 'Scalable Automation',
    description: 'Designed for flexibility & future growth.',
    textPosition: 'max-[900px]:left-[29%] md:top-1/4 left-[30%] xl:left-[29.5%] xl:top-[16%]',
  },
  {
    icon: faAward,
    title: 'Speed & Reliability',
    description: 'Fast execution with long-term support.',
    textPosition: 'max-[900px]:left-[47%] top-[55%] left-[49%] xl:left-[48%]',
  },
  {
    icon: faLightbulb,
    title: 'Frugal Solutions',
    description: 'High quality, performance, and cost-effectiveness.',
    textPosition: ' md:left-[65%] md:top-1/4 xl:left-[66%] xl:top-[16%]' ,
  },
];