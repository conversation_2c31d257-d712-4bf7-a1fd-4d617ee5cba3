import SectionTitle from '../../../../components/SectionTitle';
import circleYallow from '../../../../assets/images/mainPage/circle.png';
import ProductCard from './ProductCard';
import { useProductsApi } from '../../../../services/api/use-product-api'; 

const ProductsSection = () => {

    const { useGetProducts } = useProductsApi();
    const { data: response, isLoading, isError } = useGetProducts({ params: { per_page: 50 } });
    const productsToShow = response?.data.filter(product => product.is_home) || [];

    if (isLoading) {
        return <div className="text-center p-20">Loading...</div>;
    }

    if (isError) {
        return <div className="text-center p-20 text-red-500">Error loading products.</div>;
    }

    return (
        <section className="overflow-hidden p-5 md:p-[60px] xl:p-main-padding-large 2xl:container mx-auto">
            <SectionTitle title="Products Range" className="items-center" />
            <div className='relative'>
                <img
                    src={circleYallow}
                    alt="circle Yallow"
                    className="w-fit absolute top-[25px] left-[35px] -translate-x-1/3 -translate-y-1/3 z-0"
                    loading="lazy"/>
                <div className="relative grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 mt-12 z-10">
                    {productsToShow.map((product) => (
                        <ProductCard
                            key={product.id}
                            imageSrc={product.images?.[0]?.url}
                            title={product.name}
                            viewLink={`/products/${product.id}`}
                        />
                    ))}
                </div>
                <img
                    src={circleYallow}
                    alt="circle Yallow"
                    className="absolute bottom-[25px] right-[35px] translate-x-1/3 translate-y-1/3 z-0"
                />
            </div>
        </section>
    );
}

export default ProductsSection;