export interface MegaMenuLink {
  id: number;
  text: string;
  path: string;
}

export interface MegaMenuSubgroup {
  id: number;
  title: string;
  path?: string;
  links: MegaMenuLink[];
}

export interface MegaMenuColumn {
  title: string; 
  subgroups: MegaMenuSubgroup[];
}

export interface NavLinkItem {
  text: string;
  path?: string;
  type: 'page' | 'section' | 'dropdown' | 'megaMenu';
  children?: NavLinkItem[];
  megaMenuColumns?: MegaMenuColumn[];
}



export const NavLinkData: NavLinkItem[] = [
  {
    text: 'Home',
    path: '/',
    type: 'page',
  },
  {
    text: 'About Us',
    type: 'dropdown',
    children: [
      { text: 'Who We Are', path: '/who-we-are', type: 'page' },
      { text: 'CSR and CG', path: '/csr-and-cg', type: 'page' },
      { text: 'Commitment to Safety', path: '/our-commitment-to-safety', type: 'page' },
      { text: 'Licenses & Certification', path: '/licences-and-certifications', type: 'page' },
    ],
  },
  {
    text: 'Products',
    type: 'megaMenu',
  },
  {
    text: 'Services',
    path: '/#services',
    type: 'section',
  },
  {
    text: 'Why Choose Us',
    path: '/#WhyChooseUs',
    type: 'section',
  },
  {
    text: 'Portfolio',
    path: '/portfolio',
    type: 'page',
  },
  {
    text: 'Contact Us',
    path: '/#contact',
    type: 'section',
  },
];