
interface SafetySectionProps {
  introParagraph: string;
  title: string;
  bodyParagraphs: string[];
  mainImage: string;
}

const SafetySection: React.FC<SafetySectionProps> = ({
  introParagraph,
  title,
  bodyParagraphs,
  mainImage,
}) => {
  return (
    <section className="2xl:container mx-auto  p-4 md:p-[60px] xl:p-main-padding-large">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 items-center">

        <div className="bg-gradient-to-r from-gray-100 to-white rounded-4xl text-secondary-01 p-5">
          <p className="leading-relaxed mb-6">
            {introParagraph}
          </p>
          
          <h2 className="text-xl lg:text-3xl font-bold text-primary-01 mb-4">
            {title}
          </h2>
          
          {bodyParagraphs.map((paragraph, index) => (
            <p key={index} className="leading-relaxed mb-4">
              {paragraph}
            </p>
          ))}
        </div>
        <div className="relative order-first lg:order-last">
          <img
            src={mainImage}
            alt="Safety inspection"
            className="w-full h-auto rounded-xl shadow-lg"
            loading="lazy"
          />
         
        </div>

      </div>
    </section>
  );
};

export default SafetySection;