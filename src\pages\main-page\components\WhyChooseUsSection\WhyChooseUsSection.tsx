import FeatureLineBg from "../../../../assets/images/mainPage/timeline.svg";
import TextBlock from "./TextBlock";
import { featuresData } from "../../../../data/FeaturesData";
import SectionTitle from "../../../../components/SectionTitle";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const WhyChooseUsSection = () => {
  return (
    <section id="WhyChooseUs" className=" 2xl:container mx-auto  w-full ">
      <SectionTitle
        title="Why Choose Us?"
        className="items-center p-5 md:p-[60px] xl:p- main-padding-large"
        description="Let's build the future of industrial automation together. Contact us today to explore how we can optimize your operations!. Here's why we stand out:"
        descStyle="text-center mt-4 max-w-3/4"
      />
      <div className=" hidden md:block h-[400px]">
        <div className="relative w-full h-full">
          <img
            src={FeatureLineBg}
            alt="Features timeline background"
            className="absolute top-1/2 left-0 w-full -translate-y-1/2"
            loading="lazy"
          />
          {featuresData.map((feature, index) => (
            <div key={index}>
              <div className={`absolute ${feature.textPosition}`}>
                <TextBlock
                  title={feature.title}
                  description={feature.description}
                  className="text-center md:max-w-[190px] lg:max-w-56 xl:max-w-[70%]"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="p-5 md:p-[60px] xl:p- main-padding-large grid grid-cols-1 sm:grid-cols-2 gap-y-12 gap-x-8 md:hidden">
        {featuresData.map((feature, index) => (
          <div key={index} className="flex flex-col items-center text-center">
            <FontAwesomeIcon
              icon={feature.icon}
              className="text-secondary-02 scale-150 mb-5"
            />
            <TextBlock
              title={feature.title}
              description={feature.description}
            />
          </div>
        ))}
      </div>
    </section>
  );
};

export default WhyChooseUsSection;
