import { useState, useRef, useEffect } from 'react';
import ProductCardSlide from './ProductCardSlide';
import SectionTitle from '../../../components/SectionTitle';
import type { Product } from '../../../services/api/use-product-api';

interface RelatedProductSliderProps {
    products: Product[];
}

const RelatedProductSlider: React.FC<RelatedProductSliderProps> = ({ products }) => {
    // Current horizontal position of the slider in pixels.
    const [offset, setOffset] = useState(0);
    const [isDragging, setIsDragging] = useState(false);

    // Stores drag start position and initial slider offset.
    const dragStartInfo = useRef({ startX: 0, startOffset: 0 });

    // Refs to measure DOM elements for calculating boundaries.
    const containerRef = useRef<HTMLDivElement>(null);
    const trackRef = useRef<HTMLDivElement>(null);
    const [maxOffset, setMaxOffset] = useState(0);

    // Effect to calculate slider boundaries to prevent over-scrolling.
    useEffect(() => {
        const calculateBounds = () => {
            if (trackRef.current && containerRef.current) {
                const trackWidth = trackRef.current.scrollWidth;
                const containerWidth = containerRef.current.offsetWidth;

                // Max draggable distance is the hidden part of the track.
                setMaxOffset(Math.max(0, trackWidth - containerWidth));
            }
        };

        calculateBounds();

        window.addEventListener('resize', calculateBounds);
        return () => window.removeEventListener('resize', calculateBounds);
    }, [products]);


    const handleDragStart = (clientX: number) => {
        setIsDragging(true);
        dragStartInfo.current = { startX: clientX, startOffset: offset };
    };

    const handleDragMove = (clientX: number) => {
        if (!isDragging) return;

        const dragDistance = clientX - dragStartInfo.current.startX;
        const proposedOffset = dragStartInfo.current.startOffset + dragDistance;

        // Clamp the offset to stay within the calculated boundaries.
        const newOffset = Math.max(-maxOffset, Math.min(0, proposedOffset));

        setOffset(newOffset);
    };

    const handleDragEnd = () => {
        setIsDragging(false);
    };

    // Event Handlers
    const onTouchStart = (e: React.TouchEvent) => handleDragStart(e.targetTouches[0].clientX);
    const onTouchMove = (e: React.TouchEvent) => handleDragMove(e.targetTouches[0].clientX);
    const onMouseDown = (e: React.MouseEvent) => {
        e.preventDefault();
        handleDragStart(e.clientX);
    };
    const onMouseMove = (e: React.MouseEvent) => handleDragMove(e.clientX);

    if (!products || products.length === 0) return null;

    return (
        <div className="container mx-auto w-full p-4 md:p-[60px] xl:p-main-padding-large">
            <div className="text-center mb-8">
                <SectionTitle title='Related Product' className='items-center w-fit' />
            </div>
            <div
                ref={containerRef}
                className="relative overflow-hidden cursor-grab active:cursor-grabbing"
                onMouseDown={onMouseDown}
                onMouseMove={onMouseMove}
                onMouseUp={handleDragEnd}
                onMouseLeave={handleDragEnd}
                onTouchStart={onTouchStart}
                onTouchMove={onTouchMove}
                onTouchEnd={handleDragEnd}
            >
                <div
                    ref={trackRef}
                    className="flex gap-6 py-4"
                    style={{
                        transform: `translateX(${offset}px)`,
                        transition: 'none',
                    }}
                >
                    {products.map((product) => (
                        <ProductCardSlide key={product.id} product={product} />
                    ))}
                </div>
            </div>
        </div>
    );
};

export default RelatedProductSlider;