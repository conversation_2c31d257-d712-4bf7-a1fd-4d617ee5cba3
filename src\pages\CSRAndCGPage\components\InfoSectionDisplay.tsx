import { faCheck } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

interface CommitmentSection {
  title: string;
  description: string;
  items: string[];
}

interface GovernanceSection {
  title: string;
  content: string;
}

type AnySection = CommitmentSection | GovernanceSection;


interface InfoDisplayProps {
  sections: AnySection[];
}

const InfoSectionDisplay: React.FC<InfoDisplayProps> = ({ sections }) => {
  return (
    <div data-aos="fade-up" className="bg-white 2xl:container mx-auto p-4 md:p-[60px] xl:p-main-padding-large" >
        <div className="flex flex-col gap-y-8">
          {sections.map((section, index) => (
            <div
              key={index}
              className="bg-gradient-to-r from-gray-100 to-white rounded-4xl p-3 md:p-6 transition-shadow hover:shadow-md"
            >
              <h2 className="text-2xl md:text-3xl font-bold text-primary-01 mb-4">
                {section.title}
              </h2>

              {'content' in section && (
                <p className="text-secondary-01 text-sm md:text-base leading-relaxed mb-2">
                  {section.content}
                </p>
              )}

              {'description' in section && (
                <>
                  <p className="text-secondary-01 text-sm md:text-base leading-relaxed mb-2">
                    {section.description}
                  </p>
                  {section.items && (
                    <ul className="mt-3 space-y-2">
                      {section.items.map((item, itemIndex) => (
                        <li key={itemIndex} className="flex items-start">
                           <FontAwesomeIcon icon={faCheck} className="text-primary-01 mr-2"/>
                          <span className="text-secondary-01 text-sm md:text-base">
                            {item}
                          </span>
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              )}
            </div>
          ))}
        </div>
    </div>
  );
};

export default InfoSectionDisplay;