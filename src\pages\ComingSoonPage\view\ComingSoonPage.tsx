import React from 'react';
import { Link } from 'react-router-dom';

const ComingSoonPage: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 text-secondary-01 p-6 font-sans">
      <div className="text-center p-8 bg-white rounded-2xl shadow-2xl max-w-md w-full transform transition-all duration-500 hover:scale-105">
        <div className="mb-6">
          {/* SVG Icon for a clock */}
          <svg className="w-20 h-20 mx-auto text-primary-01 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h1 className="text-4xl md:text-5xl font-bold text-primary-01 mb-4">
          Coming Soon
        </h1>
        <p className="text-base md:text-lg text-secondary-01 mb-8">
          We are working hard to bring this page to you. It's under construction and will be available shortly. Thank you for your patience!
        </p>
        <Link
          to="/"
          className="inline-block px-8 py-3 bg-primary-01/90  text-secondary-01  font-semibold rounded-lg shadow-md hover:bg-primary-01  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-800 transition-all duration-300 ease-in-out"
        >
          Go Back to Homepage
        </Link>
      </div>
    </div>
  );
};

export default ComingSoonPage;
