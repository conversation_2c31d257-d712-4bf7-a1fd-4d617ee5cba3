import bgHero1 from "../../../assets/images/mainPage/bg-hero1.jpg";
import bgHero2 from "../../../assets/images/mainPage/bg-hero2.jpg";
import AboutSection from "../components/AboutSection";
import ProductsSection from "../components/ProductsSection/ProductsSection";
import OurServicesSection from "../components/ServicesSection/OurServicesSection";
import WhyChooseUsSection from "../components/WhyChooseUsSection/WhyChooseUsSection";
import ClientsSlider from "../components/ClientsSlider";
import OurProjectsSection from "../components/OurProjectsSection/OurProjectsSection";
import GetInTouchSection from "../components/GetInTouchSection";
import ImageGridSection from "../components/ImageGridSection";
import { ourClients } from "../../../data/OurClientsData";
import {
  OfficialData,
  PartnersData,
} from "../../../data/PartnersAndOfficialData";
import CommonHero from "../../../sections/CommonHero";
import { useEffect } from "react";
import { useLocation } from "react-router-dom";

const slides = [{ image: bgHero1 }, { image: bgHero2 }];

const MainPage = () => {
  const location = useLocation();

  useEffect(() => {
    if (location.hash) {
      const element = document.getElementById(location.hash.substring(1));
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      }
    }
  }, [location]);
  return (
    <div>
      <CommonHero
        slides={slides}
        title="Bridging the digital space"
        description="HAZ Company your solution partner for Excellence, Reliability, Speed."
      />
      <AboutSection />
      <ImageGridSection title="Our Partners" images={PartnersData} />
      <ImageGridSection
        title="Official MemberShip"
        images={OfficialData}
        variant="uniform"
      />
      <ProductsSection />
      <OurServicesSection />
      <WhyChooseUsSection />
      <ClientsSlider clients={ourClients} slideInterval={11000} />
      <OurProjectsSection />
      <GetInTouchSection />
    </div>
  );
};

export default MainPage;
