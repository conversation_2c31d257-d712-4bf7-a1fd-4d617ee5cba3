import useApiServices from "../hooks/use-api-services";
import axiosInstance from "../../utils/axios";

export interface Media {
  id: number;
  url: string;
  type: string;
}

export interface PortfolioDescription {
  id: number;
  portfolio_id: number;
  description: string;
  order: number;
  media: Media[];
}

export interface Portfolio {
  id: number;
  title: string;
  descriptions: PortfolioDescription[];
}

export interface PortfoliosListResponse {
  status: string;
  message: string;
  data: Portfolio[];
  pagination: {
    total: number;
    count: number;
    per_page: number;
    current_page: number;
    total_pages: number;
  };
}

export const PortfolioEndpoints = {
  list: "/portfolios",    
  details: "/portfolios", 
};


export const usePortfoliosApi = () => {
  const { useGetListService, useGetItemService } = useApiServices({ axiosInstance });

  // Hook to get the list of portfolios
  const useGetPortfolios = () => {
    return useGetListService<PortfoliosListResponse>({
      url: PortfolioEndpoints.list,
    });
  };

  // Hook to get a single portfolio by its ID
  const useGetPortfolio = (id: number) => {
    return useGetItemService<Portfolio>({
      url: PortfolioEndpoints.details,
      id: id.toString(),
    });
  };

  return {
    useGetPortfolios,
    useGetPortfolio,
  };
};