import { useState, useEffect } from 'react';

interface ImageGalleryProps {
  images: string[];
  productName: string;
}

export const ImageGallery: React.FC<ImageGalleryProps> = ({ images, productName }) => {
  const [activeImage, setActiveImage] = useState<string>(images[0]);
  const [animationKey, setAnimationKey] = useState<number>(0);

  useEffect(() => {
    if (images.length > 0) {
      setActiveImage(images[0]);
    }
  }, [images]);

  const handleThumbnailClick = (imageUrl: string) => {
    if (imageUrl !== activeImage) {
      setActiveImage(imageUrl);
      setAnimationKey(prevKey => prevKey + 1);
    }
  };

  if (!images || images.length === 0) {
    return <div>No images available.</div>;
  }

  return (
    <div className="flex flex-row-reverse md:flex-row items-center gap-4">
      <div className="flex-grow w-full">
        <div className="relative w-[90%] h-[90%] m-auto bg-primary-01/80 rounded-full shadow-xl border border-primary-01 p-1">
          <div data-aos="zoom-in" className="relative w-full bg-white rounded-full aspect-square overflow-hidden">
            <img
              src={activeImage}
              alt=""
              aria-hidden="true"
              className="absolute inset-0 w-full h-full object-cover rounded-full filter blur-lg scale-110"
            />
            <div className="absolute inset-0 w-full h-full bg-white/50 rounded-full"></div>

            <img
              key={animationKey}
              src={activeImage}
              alt={productName}
              className="absolute inset-0 w-full h-full object-contain rounded-full animate-crossFadeZoomIn p-2"
              loading="lazy"/>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-3">
        {images.map((imgUrl, index) => (
          <button
            key={index}
            onClick={() => handleThumbnailClick(imgUrl)}
            className={`w-20 h-20 md:w-24 md:h-24 rounded-full p-1 transition-all duration-300 cursor-pointer overflow-hidden ${
              activeImage === imgUrl ? 'border-2 border-yellow-400' : 'border border-gray-300'
            }`}
          >
            <img
              src={imgUrl}
              alt={`Thumbnail ${index + 1}`}
              className="w-full h-full object-cover rounded-full"
              loading="lazy"
            />
          </button>
        ))}
      </div>
    </div>
  );
};