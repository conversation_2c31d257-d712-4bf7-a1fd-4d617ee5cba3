import { faDownload } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

interface DownloadButtonProps {
  filePath: string;
  buttonText: string;
  fileName?: string;
  className?: string;
}

const DownloadButton: React.FC<DownloadButtonProps> = ({ 
  filePath, 
  buttonText, 
  fileName,
  className = '' 
}) => {
  return (

    <a
      href={filePath}
      download={fileName || true} 
      className={`
        inline-flex items-center justify-center gap-3 px-6 py-3 
        bg-primary-01/90 text-secondary-01 font-bold rounded-full 
        shadow-md hover:bg-primary-01 focus:outline-none 
        focus:ring-2 focus:ring-primary-01 focus:ring-offset-2 
        transition-all duration-300 ease-in-out transform hover:-translate-y-1
        ${className}
      `}
      role="button"
      aria-label={`Download ${buttonText}`}
    >
      <FontAwesomeIcon icon={faDownload}/>
      <span>{buttonText}</span>
    </a>
  );
};

export default DownloadButton;