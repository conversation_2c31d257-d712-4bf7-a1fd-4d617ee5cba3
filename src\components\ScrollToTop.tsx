import { useEffect, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowUp } from '@fortawesome/free-solid-svg-icons';

export default function ScrollToTop() {
  const [showScrollTo, setShowScrollTo] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 500) {
        setShowScrollTo(true);
      } else {
        setShowScrollTo(false);
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const handleScrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="fixed inset-0 z-50 pointer-events-none">
      <div className="max-w-screen-2xl mx-auto h-full relative">
        <div
          onClick={handleScrollToTop} 
          className={`${
            showScrollTo ? "translate-x-0" : "translate-x-40"
          } w-11 h-11 p-2.5 cursor-pointer rounded-full 
             transition-transform duration-300 ease-in-out absolute bottom-6 right-5 md:right-[60px]  xl:right-main-padding-large 
             z-40 bg-primary-01 flex justify-center items-center 
             shadow-sm shadow-secondary-01 pointer-events-auto`}  
        >
          <FontAwesomeIcon icon={faArrowUp} className='text-secondary-01' />
        </div>
      </div>
    </div>
  );
}