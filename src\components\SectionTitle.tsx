
interface SectionTitleProps {
  title: string;
  className?: string;
  description?: string;
  info?: React.ReactNode;
  textColor?:string;
  descStyle?: string;
  spanColor?:string
}

const SectionTitle: React.FC<SectionTitleProps> = ({ title, className = '', description , info, textColor, descStyle,spanColor}) => {
  return (
    <div className={`2xl:container mx-auto flex flex-col justify-center ${className}`}>
       <div className="relative  w-fit">
        <span 
          className={`${spanColor ? spanColor : 'bg-primary-01'} absolute bottom-0 -translate-y-1/2 left-0 w-full h-3 rounded-lg z-10`}
          aria-hidden="true"
        ></span>
        <h2 
          className={`relative text-2xl sm:text-3xl lg:text-4xl font-bold z-20 ${textColor ? textColor : 'text-secondary-01'}`} >
          {title}
        </h2>
      </div>
      <p className={`${descStyle} text-secondary-01`}>{description}</p>
      <p>{info}</p>
    
    </div>
  );
};

export default SectionTitle;