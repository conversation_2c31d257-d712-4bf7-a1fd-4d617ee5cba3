import CommonButton from "../../../../components/CommonButton";

export interface ProductCardProps {
  imageSrc: string;
  title: string;
  viewLink: string;
}

const ProductCard: React.FC<ProductCardProps> = ({ imageSrc, title, viewLink }) => {
  return (
    <div className="bg-white rounded-2xl shadow-[6px_8px_18.6px_0px_#3C3C3B1A] flex  items-stretch 
        transition-all ease-in-out duration-500 hover:shadow-xl hover:-translate-y-1.5 overflow-hidden">

      <div className=" h-48 max-[360px]:w-30 w-40 flex-shrink-0">
        <img 
          src={imageSrc} 
          alt={title} 
          className="w-full h-full object-cover" 
          loading="lazy"
        />
      </div>

      <div className="flex flex-col  justify-center gap-2.5 items-start text-left p-4 flex-grow">
        <h3 className="text-secondary-01 font-bold text-xl md:text-2xl uppercase">
          {title}
        </h3>
        <CommonButton path={viewLink} text='View' style="text-sm scale-100 hover:scale-110 transition-all" textColor="text-primary-01"/>
      </div>
    </div>
  );
};

export default ProductCard;