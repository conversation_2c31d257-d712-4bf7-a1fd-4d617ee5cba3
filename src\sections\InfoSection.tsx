import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { whatWeDoList, type CircleData } from "../data/AboutSectionData";
import FloatingCircle from "../components/FloatingCircle";
import SectionTitle from "../components/SectionTitle";
import { faCaretRight } from "@fortawesome/free-solid-svg-icons";
import { AboutValuesData } from "../data/AboutValuesData";
import { Link } from "react-router-dom";

interface InfoSectionProps {
  leftContent: string;
  title: string;
  info: string;
  buttonText?: string;
  circles?: CircleData[];
  isVision?: boolean;
  isMission?: boolean;
}

const InfoSection: React.FC<InfoSectionProps> = ({
  leftContent,
  title,
  info,
  circles,
  isVision,
  isMission,
}) => {
  return (
    <>
      <div
        className={`${
          isVision
            ? "max-[450px]:grid-cols-1 grid-cols-2"
            : "max-[1260px]:grid-cols-1 grid-cols-2"
        } 
    ${
      isMission ? "flex flex-row-reverse justify-between items-center" : "grid"
    }  lg:gap-10 xl:gap-main-padding-large items-center`}
      >
        <div
          className={`${
            isMission ? "lg:w-1/2  " : "w-full lg:flex-0 "
          } relative  flex flex-wrap justify-center items-center gap-6 mb-8 lg:mb-0 `}
        >
          <div className="w-full h-full">
            <img
              src={leftContent}
              alt="image"
              className={`${
                isVision || isMission
                  ? "max-[450px]:hidden block"
                  : "max-[1260px]:hidden block"
              } `}
              loading="lazy"
            />
          </div>

          {circles &&
            circles.map((circle, index) => (
              <FloatingCircle
                key={index}
                icon={circle.icon}
                title={circle.title}
                subtitle={circle.subtitle}
                sizeClasses={circle.sizeClasses}
                positionClasses={circle.positionClasses}
                animationClass={circle.animationClass}
                animationDelay={circle.animationDelay}
                colorText={circle.colorText}
              />
            ))}
        </div>
        {isVision || isMission ? (
          <div data-aos="fade-up">
            <SectionTitle
              title={title}
              description={info}
              descStyle="md:text-lg xl:text-2xl"
              className="text-start w-fit gap-1 md:gap-3"
              spanColor={isMission ? "bg-white" : ""}
            />
          </div>
        ) : (
          <div data-aos="fade-up" className="text-start">
            <div>
              <SectionTitle
                title={title}
                description={info}
                descStyle="md:text-lg xl:text-xl"
                className="text-start w-fit gap-1 md:gap-3"
                spanColor={isMission ? "bg-white" : ""}
              />
            </div>
            <div className="mt-12">
              <h2
                className={`relative text-xl sm:text-2xl lg:text-3xl font-semibold text-secondary-01 mb-4`}
              >
                What We Do
              </h2>
              <ul className="space-y-3">
                {whatWeDoList.map((item, index) => (
                  <li
                    key={index}
                    className="flex items-center md:text-lg xl:text-xl text-secondary-02"
                  >
                    <span className="inline-block w-3 h-3 md:w-4 md:h-4 bg-primary-01 rounded-full mr-2 md:mr-4 flex-shrink-0"></span>
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
      {!isMission && !isVision && (
        <div className="mt-20 relative w-[90%] mx-auto  bg-secondary-01 rounded-4xl text-white  shadow-lg">
          <div className="flex flex-col md:flex-row items-center justify-around gap-x-3 gap-y-6 lg:gap-x-5 p-6 ">
            {AboutValuesData.map((item, index) => (
              <div key={index}>
                <div className="flex-1 flex flex-col items-center text-center ">
                  <h3 className="text-2xl font-semibold mb-4">{item.title}</h3>
                  <p className="text-base lg:text-lg italic font-light mb-4 px-3">
                    {item.quote}
                  </p>
                  <cite className="text-[12px] italic  text-primary-01">
                    {item.author}
                  </cite>
                </div>
              </div>
            ))}
          </div>
          <Link
            to={"/who-we-are"}
            className="absolute top-1/2 -translate-y-1/2 right-0 translate-x-1/2 bg-primary-01 transition-colors 
        w-12 h-12 sm:w-16 sm:h-16 rounded-full flex items-center justify-center shadow-lg 
        focus:outline-none focus:ring-4 focus:ring-yellow-300"
            aria-label="Next"
          >
            <FontAwesomeIcon
              icon={faCaretRight}
              className="scale-200 p-4 hover:scale-[250%] hover:rotate-180 transition-all duration-300 ease-in-out"
            />
          </Link>
        </div>
      )}
    </>
  );
};

export default InfoSection;
