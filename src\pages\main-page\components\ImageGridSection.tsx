import SectionTitle from "../../../components/SectionTitle";

interface ImageGridSectionProps {
  title: string;
  images: string[];
  variant?: 'default' | 'uniform';
}

const ImageGridSection = ({ title, images, variant = 'default' }: ImageGridSectionProps) => {
  return (
    <section className="2xl:container mx-auto px-5 md:px-[60px] xl:px-main-padding-large pb-[72px]">
      <SectionTitle title={title} className="items-center" />

      <div data-aos="fade-up" className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-8 mt-12">
        {images.map((img, index) => {
          if (variant === 'uniform') {
            return (
              <div
                key={index}
                className="flex items-center justify-center h-24 w-32 md:w-44 mx-auto" 
              >
                <img
                  src={img}
                  alt={`${title} - Image ${index + 1}`}
                  className="max-w-[90%] max-h-[90%] scale-80 object-contain"
                  loading="lazy"/>
              </div>
            );
          } else {
            return (
              <img
                key={index}
                src={img}
                alt={`${title} - Image ${index + 1}`}
                className="m-auto scale-80 max-w-[90%] w-40 md:w-44 mx-auto"
                loading="lazy"/>
            );
          }
        })}
      </div>
    </section>
  );
};

export default ImageGridSection;