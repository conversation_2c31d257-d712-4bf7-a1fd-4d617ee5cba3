import project1 from "../assets/images/mainPage/project1.jpg";
import project2 from "../assets/images/mainPage/project2.jpg";
import project3 from "../assets/images/mainPage/project3.jpg";

export interface ProjectCardData {
  id: number;
  type: "text" | "image";
  title?: string;
  description?: string;
  buttonText?: string;
  imageUrl?: string;
  altText?: string;
  gridClasses: string;
  bgColor?: "bg-secondary-01" | "bg-primary-01";
  hoverText?: string;
  path?: string;
}

export const projectsData: ProjectCardData[] = [
  {
    id: 1,
    type: "text",
    title: "Our Projects",
    description:
      "Our solutions ensure smooth material flow, higher throughput, and greater efficiency in warehouses and production lines",
    gridClasses:
      "col-span-2 sm:col-span-3 lg:col-span-2 p-10 sm:p- main-padding-large",
    bgColor: "bg-secondary-01",
  },
  {
    id: 2,
    type: "image",
    imageUrl: project1,
    altText: "Large warehouse conveyor system",
    gridClasses: "lg:col-span-1 lg:row-span-1",
    hoverText:
      "Engineered with smart material selection and precision fabrication.",
  },
  {
    id: 3,
    type: "text",
    title: "CONVEYOR SYSTEMS",
    buttonText: "View All",
    gridClasses: "lg:col-span-1",
    bgColor: "bg-primary-01",
    path: "/portfolio?category=conveyor-systems",
  },
  {
    id: 4,
    type: "text",
    title: "PROCESSING WORKSTATIONS",
    buttonText: "View All",
    gridClasses: "lg:col-span-1",
    bgColor: "bg-primary-01",
    path: "/portfolio?category=industrial-processing",
  },
  {
    id: 5,
    type: "image",
    imageUrl: project2,
    altText: "Processing workstation",
    gridClasses: "lg:col-span-1",
    hoverText:
      "Engineered with smart material selection and precision fabrication.",
  },
  {
    id: 6,
    type: "text",
    title: "SAFETY SYSTEMS",
    buttonText: "View All",
    gridClasses: "lg:col-span-1",
    bgColor: "bg-secondary-01",
    path: "/portfolio?category=safety-systems",
  },
  {
    id: 7,
    type: "image",
    imageUrl: project3,
    altText: "Yellow safety gate in a factory",
    gridClasses: "lg:col-span-1",
    hoverText:
      "Engineered safety solutions designed to enhance workplace protection.",
  },
];
