export interface ValueCardData {
  title: string;
  quote: string;
  author: string;
  tagTheme: 'yellow' | 'dark'; 
}


export const coreValuesData: ValueCardData[] = [
  { title: 'HONESTY', quote: 'Honesty is the best policy.', author: 'ABRAHAM LINCOLN', tagTheme: 'yellow' },
  { title: 'SENSE OF URGENCY', quote: 'Real artists ship.', author: 'STEVE JOBS', tagTheme: 'yellow' },
  { title: 'PROFESSIONALISM', quote: 'Act as if what you do makes a difference. It does.', author: 'PETER DRUCKER', tagTheme: 'dark' },
  { title: 'PROACTIVE', quote: 'The best way to predict the future is to create it.', author: '<PERSON><PERSON><PERSON>AM<PERSON> FRANKLIN', tagTheme: 'dark' },
  { title: 'ACCOUNTABILITY', quote: 'Accountability breeds response-ability.', author: 'STEPHEN COVEY', tagTheme: 'yellow' },
  { title: 'FEEDBACK', quote: 'Your most unhappy customers are your greatest source of learning.', author: '<PERSON><PERSON>L GATES', tagTheme: 'yellow' },
  { title: 'FOLLOWING-UP', quote: 'Diligence is the mother of good luck.', author: 'B<PERSON><PERSON>AM<PERSON> FRANKL<PERSON>', tagTheme: 'dark' },
  { title: 'CUSTOMER-FOCUS', quote: `We’re not competitor obsessed, we’re customer obsessed.`, author: 'JEFF BEZOS', tagTheme: 'dark' },
  { title: 'INFORMATIVE', quote: 'Tell people what you\'re going to tell them, tell them, then tell them what you told them.', author: 'DALE CARNEGIE', tagTheme: 'yellow' },
  { title: 'INNOVATION', quote: 'If you always do what you always did, you will always get what you always got.', author: 'ALBERT EINSTEIN', tagTheme: 'yellow' },
];
