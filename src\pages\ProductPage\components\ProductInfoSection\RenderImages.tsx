import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlay, faTimes } from '@fortawesome/free-solid-svg-icons';
import type { ProductMedia } from '../../../../services/api/use-product-api';

interface RenderImagesProps {
  images: ProductMedia[];
  imageLayout: 'single' | 'multiple' | 'video_promo';
  videoUrl?: string;
}

const RenderImages: React.FC<RenderImagesProps> = ({ images, imageLayout, videoUrl }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  if (!images || images.length === 0) {
    return null;
  }

  const handlePlayClick = () => {
    if (videoUrl) {
      setIsModalOpen(true);
    }
  };

  const hasVideo = imageLayout === 'video_promo' && videoUrl;

  switch (imageLayout) {
    case 'video_promo':
      return (
        <>
          <div className="relative mx-auto flex w-3/4 aspect-square items-center justify-center rounded-full bg-white border-4 border-primary-01">
            <img src={images[0].url} alt="Specification" className="relative z-10 h-[95%] w-[95%] rounded-full object-cover" loading="lazy"/>
            
            {/* The play button only renders if 'hasVideo' is true */}
            {hasVideo && (
              <button 
                type="button" 
                aria-label="Play Video"
                onClick={handlePlayClick}
                className="absolute max-[470px]:bottom-0 max-[470px]:right-2 bottom-3 right-3 sm:max-[900px]:bottom-7 sm:max-[900px]:right-5 
                min-[900px]:max-[1024px]:bottom-20 
                z-20 flex h-20 w-20 md:h-24 md:w-24 items-center justify-center rounded-full border-8 border-white 
                bg-primary-01 transition-transform hover:scale-110 cursor-pointer"
              >
                <FontAwesomeIcon icon={faPlay} className="text-2xl md:text-3xl text-white" />
              </button>
            )}
          </div>

          {/* The video modal */}
          {isModalOpen && videoUrl && (
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 bg-opacity-75" onClick={() => setIsModalOpen(false)}>
              <div className="relative w-full max-w-4xl p-4" onClick={(e) => e.stopPropagation()}>
                <button 
                  onClick={() => setIsModalOpen(false)}
                  className="absolute -top-10 right-0 text-white text-3xl hover:text-gray-300"
                  aria-label="Close video player"
                >
                  <FontAwesomeIcon icon={faTimes} />
                </button>
                <video src={videoUrl} controls autoPlay className="w-full h-auto" />
              </div>
            </div>
          )}
        </>
      );

    case 'multiple':
      return (
        <div className="flex flex-col md:flex-row justify-center gap-4">
          {images.map((image) => (
            <div key={image.id} className="w-full md:w-1/2 rounded-full overflow-hidden">
              <img
                src={image.url}
                alt={`Application ${image.id}`}
                className="object-cover w-full h-auto aspect-square rounded-full"
                loading="lazy"
              />
            </div>
          ))}
        </div>
      );
    
    case 'single':
    default:
       return <img src={images[0].url} alt="Benefit" className="rounded-lg object-contain w-full h-full" loading="lazy"/>;
  }
};

export default RenderImages;