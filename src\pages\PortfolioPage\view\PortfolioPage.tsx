import { useState, useMemo, useEffect } from 'react';
import CommonHero from '../../../sections/CommonHero';
import Information from '../components/Information';
import { FilterTabs } from '../components/FilterTabs';
import { usePortfoliosApi } from '../../../services/api/use-portfolio-api';
import type { GridRow } from '../../../types/portfolio-types';
import heroBackground from '../../../assets/images/portfolioPage/portfolio.jpg';
import yellowLineImage from '../../../assets/images/aboutPage/banner.png';
import DownloadCard from '../../../components/DownloadCard';
import SectionTitle from '../../../components/SectionTitle';

const generateSlug = (text: string): string => {
  if (!text) return '';
  return text.toLowerCase().replace(/\s+/g, '-');
};

const PortfolioPage = () => {
  const { useGetPortfolios } = usePortfoliosApi();
  const { data: apiResponse, isLoading, isError } = useGetPortfolios();

  const hasApiData = apiResponse && apiResponse.data && apiResponse.data.length > 0;

  const categories = useMemo(() => {
    if (!hasApiData) {
      return [];
    }
    return apiResponse.data.map(portfolio => ({
      id: portfolio.id.toString(),
      title: portfolio.title,
    }));
  }, [apiResponse, hasApiData]);

  const [activeCategory, setActiveCategory] = useState<string>('');

  useEffect(() => {
    if (categories.length === 0) {
      return;
    }

    const params = new URLSearchParams(window.location.search);
    const slugFromUrl = params.get('category');

    if (slugFromUrl) {
      const matchingCategory = categories.find(
        (category) => generateSlug(category.title) === slugFromUrl
      );

      if (matchingCategory) {
        setActiveCategory(matchingCategory.id);
      } else {
        setActiveCategory(categories[0]?.id || '');
      }
    } else {
      setActiveCategory(categories[0]?.id || '');
    }
  }, [categories]);

  const displayedData = useMemo(() => {
    if (!hasApiData || !activeCategory) {
      return [];
    }

    const activePortfolio = apiResponse.data.find(p => p.id.toString() === activeCategory);
    if (!activePortfolio) {
      return [];
    }

    return activePortfolio.descriptions.map((desc, index): GridRow => ({
      id: desc.id,
      layout: index % 2 === 0 ? 'text-left' : 'text-middle',
      textBlock: {
        text: desc.description,
        bgColor: index % 2 === 0 ? 'bg-secondary-01' : 'bg-primary-01',
        textColor: index % 2 === 0 ? 'text-white' : 'text-secondary-01',
      },
      images: desc.media.map(mediaItem => ({
        src: mediaItem.url,
        alt: `Portfolio image ${mediaItem.id}`,
      })),
    }));
  }, [activeCategory, apiResponse, hasApiData]);

  if (isLoading) {
    return <div className="text-center p-24 h-screen text-primary-01 text-xl ">Loading...</div>;
  }

  if (isError) {
    return <div className="text-center p-24 h-screen text-red-500 text-xl">Error fetching data.</div>;
  }

  const activeCategoryTitle = categories.find(c => c.id === activeCategory)?.title || 'Our Portfolio';

  return (
    <div>
      <CommonHero
        image={heroBackground}
        yellowLineImage={yellowLineImage}
        title={activeCategoryTitle.toUpperCase()}
        subHero={true}
        isCenter={true}
      />
      <div className='px-5 md:px-[60px] xl:px-main-padding-large pb-5'>
         <SectionTitle title="Explore Our Solutions" className="items-center" textColor='text-secondary-01'
         description=' Get an in-depth look at our technology and capabilities with our comprehensive product catalog and company profile.'
         descStyle='text-center'/>
      <div className="mx-auto mt-12 grid max-w-lg grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-2 ">
        <DownloadCard
          title="Company Profile"
          description="Discover our story, vision, and values. This profile provides a comprehensive look at our identity, expertise, and our commitment to delivering innovative solutions for our clients."
          filePath="/files/HAZ-Company-Profile-Rev20250428.pdf"
          fileName="HAZ-Operations-Profile.pdf"
          fileType="pdf"
        />
        <DownloadCard
          title="Full Product Catalog"
          description="Explore our complete range of handling systems and logistics solutions. This guide includes detailed technical specifications, photos, and practical applications for every solution."
          filePath="/files/Catalouge-Rev20250428.pdf"
          fileName="HAZ-Operations-Catalog.pdf"
          fileType="pdf"
        />
      </div>
     </div>
      {hasApiData ? (
        <>
          <FilterTabs
            categories={categories}
            activeCategory={activeCategory}
            onSelectCategory={setActiveCategory}
          />
         
          <Information data={displayedData} />
        </>
      ) : (
        <div className="text-center p-24 text-secondary-01 text-xl">
          No Data
        </div>
      )}
    </div>
  );
};

export default PortfolioPage;