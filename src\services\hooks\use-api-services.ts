import {
  useMutation,
  useQuery,
  type MutationKey,
  type UseMutationOptions,
  type UseQueryOptions,
} from "@tanstack/react-query";
import { AxiosError, type AxiosInstance, type AxiosResponse } from "axios";
import { serialize } from "object-to-formdata";


import type {
  IGetItemServiceParams,
  IGetListServiceParams,
  IPostServiceParams,
} from "../types/api-services";

export const useApiServices = ({
  axiosInstance,
}: {
  axiosInstance: AxiosInstance;
}) => {

  const useGetListService = <DataType, ParamsType = undefined>({
    url,
    params,
    queryOptions,
  }: IGetListServiceParams<ParamsType> & {
    queryOptions?: Partial<UseQueryOptions<DataType>>;
  }) => {
    return useQuery<DataType>({
      queryKey: [url + "list", params],
      queryFn: async () => {
        const response: AxiosResponse<DataType> = await axiosInstance.get(url, {
          params,
        });
        return response.data;
      },
      ...queryOptions,
    });
  };

  const useGetItemService = <DataType>({
    url,
    id,
    queryOptions,
  }: IGetItemServiceParams & {
    queryOptions?: Omit<UseQueryOptions<DataType>, "queryKey">;
  }) => {
    return useQuery<DataType>({
      queryKey: [url + id + "item"],
      queryFn: async () => {
        const response: AxiosResponse<DataType> = await axiosInstance.get(
          `${url}/${id || ""}`
        );
        return response.data;
      },
      ...queryOptions,
    });
  };

  const usePostService = <TVars, TData>({
    url,
    withFormData = false,
    onSuccess,
    queryOptions,
  }: IPostServiceParams & {
    queryOptions?: UseMutationOptions<TData, AxiosError, TVars, MutationKey>;
  }) => {
    return useMutation<TData, AxiosError, TVars, MutationKey>({
      mutationFn: (payload) => {
        return axiosInstance.post(
          url,
          withFormData ? serialize(payload, { indices: false, noFilesWithArrayNotation: true }) : payload
        );
      },
      onSuccess(data: TData) {
        if (onSuccess) {
          onSuccess(data);
        }
      },
      ...queryOptions,
    });
  };

  return {
    useGetListService,
    useGetItemService,
    usePostService,
  };
};

export default useApiServices;