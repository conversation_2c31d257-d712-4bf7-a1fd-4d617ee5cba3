
interface TextBlock {
  text: string;
  bgColor: string;
  textColor: string;
}

interface ImageItem {
  src: string;
  alt: string;
}

export interface GridRow {
  id: number;
  layout: 'text-left' | 'text-middle';
  textBlock: TextBlock;
  images: ImageItem[];
}

interface InformationProps {
  data: GridRow[];
}

const Information: React.FC<InformationProps> = ({ data }) => {
  return (
    <section className="w-full bg-white overflow-hidden mb-1 px-5 
    md:px-[60px] xl:px-main-padding-large 2xl:container mx-auto">
        <div className="grid grid-cols-1 gap-1">
          {data.map((row) => (
            <>
            <div key={row.id} className="grid grid-cols-2 md:grid-cols-3 gap-1" >
              {row.layout === 'text-left' && (
                <>
                  <div className={`p-2 md:p-8 flex items-center col-span-2 md:col-span-1  ${row.textBlock.bgColor}`} data-aos="zoom-in">
                    <p className={`leading-relaxed text-sm md:text-base ${row.textBlock.textColor}`}>
                      {row.textBlock.text}
                    </p>
                  </div>
                    {row.images.map((img,index) => {
                        return  <img key={index} src={img.src} alt={img.alt} className="w-full h-full object-cover " data-aos="zoom-in"/>
                    })}
                </>
              )}
            </div>

            {row.layout === 'text-middle' && (
                <div className='grid grid-cols-2 md:grid-cols-4 gap-1'>
                  <div className={`p-2 md:p-8 flex items-center  ${row.textBlock.bgColor}`} data-aos="zoom-in">
                    <p className={`leading-relaxed text-sm md:text-base ${row.textBlock.textColor}`}>
                      {row.textBlock.text}
                    </p>
                  </div>
                      {row.images.map((img,index) => {
                        return  <img key={index} src={img.src} alt={img.alt} className="w-full h-full  object-cover " data-aos="zoom-in" />
                    })}
                  </div>
              )}
              </>
          ))}
        </div>
    </section>
  );
};

export default Information;