import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import SectionTitle from '../../../components/SectionTitle';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons';

const useWindowSize = () => {
  const [windowSize, setWindowSize] = useState<{ width: number | undefined }>({
    width: undefined,
  });

  useEffect(() => {
    const handleResize = () => setWindowSize({ width: window.innerWidth });
    window.addEventListener('resize', handleResize);
    handleResize();
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return windowSize;
};

interface ClientsSliderProps {
  clients: string[]; 
  slideInterval?: number; 
}

const ClientsSlider: React.FC<ClientsSliderProps> = ({ clients, slideInterval = 8000 }) => {
  const { width } = useWindowSize();
  const transitionDuration = 1000; 

  const itemsPerPage = useMemo(() => {
    if (!width) return 4;
    if (width < 768) return 1;
    if (width < 1024) return 3;
    return 4;
  }, [width]);
  
  const totalPages = Math.ceil(clients.length / itemsPerPage);
  
  const [currentPage, setCurrentPage] = useState(totalPages > 1 ? 1 : 0);
  const [isTransitioning, setIsTransitioning] = useState(true);
  
  const autoplayTimer = useRef<number | null>(null);
  const transitionEndRef = useRef<number | null>(null);

  const extendedPages = useMemo(() => {
    if (totalPages <= 1) return [clients];
    
    const pages = Array.from({ length: totalPages }).map((_, i) =>
      clients.slice(i * itemsPerPage, (i + 1) * itemsPerPage)
    );

    return [pages[totalPages - 1], ...pages, pages[0]];
  }, [clients, itemsPerPage, totalPages]);

  const goToNext = useCallback(() => {
    if (totalPages <= 1) return;
    setCurrentPage(prev => prev + 1);
  }, [totalPages]);

  const goToPrevious = useCallback(() => {
    if (totalPages <= 1) return;
    setCurrentPage(prev => prev - 1);
  }, [totalPages]);

  useEffect(() => {

    if (currentPage === totalPages + 1) {
      transitionEndRef.current = window.setTimeout(() => {
        setIsTransitioning(false); 
        setCurrentPage(1);
      }, transitionDuration);
    }

    if (currentPage === 0) {
      transitionEndRef.current = window.setTimeout(() => {
        setIsTransitioning(false);
        setCurrentPage(totalPages);
      }, transitionDuration);
    }

    return () => {
      if (transitionEndRef.current) clearTimeout(transitionEndRef.current);
    };
  }, [currentPage, totalPages, transitionDuration]);

  useEffect(() => {
    if (!isTransitioning) {
      window.setTimeout(() => {
        setIsTransitioning(true);
      }, 50);
    }
  }, [isTransitioning]);
  
  const resetAutoplay = useCallback(() => {
    if (autoplayTimer.current) clearInterval(autoplayTimer.current);
    if (totalPages > 1) {
      autoplayTimer.current = window.setInterval(goToNext, slideInterval);
    }
  }, [goToNext, slideInterval, totalPages]);

  useEffect(() => {
    resetAutoplay();
    return () => {
      if (autoplayTimer.current) clearInterval(autoplayTimer.current);
    };
  }, [currentPage, resetAutoplay]);

  if (!clients.length) {
    return null;
  }

  return (
    <section className="2xl:container mx-auto px-5 md:px-[60px] xl:px-main-padding-large py-[72px]">
      <SectionTitle title="Our Clients" className="items-center"/>
      <div className="grid grid-cols-[auto_1fr_auto] items-center gap-x-4 mt-12">
        {totalPages > 1 && (
           <button
             onClick={goToPrevious}
             className="w-11 h-11 bg-primary-01/90 text-secondary-01 hover:text-white p-2 rounded-full hover:bg-primary-01 transition-colors duration-300 focus:outline-none cursor-pointer"
             aria-label="Previous Clients"
           >
             <FontAwesomeIcon icon={faChevronLeft} />
           </button>
        )}

        <div className="w-full overflow-hidden">
          <div
            className={`flex ${isTransitioning ? 'ease-in-out' : ''}`}
            style={{ 
              transform: `translateX(-${currentPage * 100}%)`,
              transition: isTransitioning ? `transform ${transitionDuration}ms ease-in-out` : 'none'
            }}
          >
            {extendedPages.map((pageClients, pageIndex) => (
              <div key={pageIndex} className="w-full flex-shrink-0 flex items-center justify-around gap-4 p-4">
                {pageClients.map((client, clientIndex) => (
                  <div key={clientIndex} className="px-6 py-4 flex-1 flex justify-center items-center h-20 bg-white rounded-full shadow-[0_0_12px_rgba(0,0,0,0.08)]">
                    <img src={client} alt='Client' className="w-full h-full object-contain" loading="lazy"/>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>

        {totalPages > 1 && (
         <button
           onClick={goToNext}
           className="w-11 h-11 bg-primary-01/90 text-secondary-01 hover:text-white p-2 rounded-full hover:bg-primary-01 transition-colors duration-300 focus:outline-none cursor-pointer"
           aria-label="Next Clients"
         >
           <FontAwesomeIcon icon={faChevronRight} />
         </button>
        )}
      </div>
    </section>
  );
};

export default ClientsSlider;