import CommonButton from "../../../components/CommonButton";
import type { Product } from '../../../services/api/use-product-api';
import { Link } from "react-router-dom";

interface ProductCardProps {
  product: Product;
}

const ProductCardSlide: React.FC<ProductCardProps> = ({ product }) => {
  const imageUrl = product.images[0]?.url || 'path/to/placeholder.jpg';
  
  return (
    <div className="flex-shrink-0 w-full sm:max-w-[300px] lg:max-w-[390px]">
      <div className="bg-white rounded-[28px] border border-gray-200 h-full flex flex-col gap-4 text-left overflow-hidden
       shadow-sm hover:shadow-lg transition-shadow duration-300 p-4">
      <div className="w-full h-64 bg-gray-50 rounded-xl">
          <img 
            src={imageUrl} 
            alt={product.name} 
            className="w-full h-full object-contain"
            loading="lazy"
          />
        </div>
        <div className="flex flex-col flex-grow text-center">
          <h3 className="text-2xl font-bold text-primary-01 mb-1">
            {product.name}
          </h3>
          
          <p className="text-secondary-01 text-xl flex-grow line-clamp-2">
            {product.mini_description}
          </p>
        </div>
        <Link to={`/products/${product.id}`} className="mx-auto">
         <CommonButton path='#' text='Learn More' style='mx-auto w-fit bg-secondary-01 py-2 px-4 lg:py-4 lg:px-8  hover:font-bold' textColor='text-white' />
        </Link>
      </div>
    </div>
  );
};

export default ProductCardSlide;