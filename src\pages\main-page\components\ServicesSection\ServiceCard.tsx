import type { IconProp } from "@fortawesome/fontawesome-svg-core";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useState, type ReactNode } from "react";
export interface ServiceCardData {
  icon: IconProp;
  title: string;
  description: string;
  hoverImage: string;
  hoverText: ReactNode;
}

const ServiceCard: React.FC<ServiceCardData> = ({ icon, title, description, hoverImage, hoverText }) => {

  const [isActive, setIsActive] = useState(false);

  const handleCardClick = () => {
    setIsActive(prevState => !prevState);
  };

  return (
    <div 
      data-aos="fade-up" 
      data-aos-duration="3000" 
      onClick={handleCardClick}
      className="relative bg-white rounded-2xl shadow-lg p-3 lg:p-6 h-[420px] group overflow-hidden cursor-pointer"
    >
      <div className="flex flex-col">
        <div className="w-full h-full text-center mb-3 py-6">
            <FontAwesomeIcon icon={icon}  className="text-6xl text-primary-01"/>
        </div>
        <h3 className="text-xl xl:text-2xl font-bold text-secondary-01 mb-2 text-center px-3">{title}</h3>
        <p className="text-secondary-03 text-base px-3">{description}</p>
      </div>

      <div className="absolute bottom-0 left-0 w-full h-[18px] bg-primary-01 mt-2"></div>
      
      <div 
        className={`absolute inset-0 bg-cover bg-center bg-no-repeat transition-transform duration-500 ease-in-out transform 
          ${isActive ? 'translate-y-0' : 'translate-y-full'} group-hover:translate-y-0`}
        style={{ backgroundImage: `url(${hoverImage})` }}
      >
        <div className="absolute inset-0 bg-primary-01/85 flex items-center justify-center p-4">
          <div className="text-white">
            <h3 className="font-bold text-xl text-center mb-3">Key Features:</h3>
            <p className="text-sm sm:text-base">{hoverText}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceCard;