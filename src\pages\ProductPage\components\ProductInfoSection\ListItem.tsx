
import CheckIcon from '../../../../assets/images/productDetailsPage/check-icon.svg';
import type { ProductSpecification, ProductKeyBenefit, ProductApplication } from '../../../../services/api/use-product-api';

interface ListItemProps {
  item: ProductSpecification | ProductKeyBenefit | ProductApplication;
  index: number; 
}

const ListItem: React.FC<ListItemProps> = ({ item, index }) => {

  const backgroundClass = index % 2 === 0 ? 'bg-gray-100' : 'bg-transparent';

  // Case 1: For items that have 'content' (like Specifications)
  if ('content' in item && item.content) {
    return (
      <li className={`flex flex-col items-start gap-1 p-3 rounded-full ${backgroundClass}`}>
        <div className='flex items-center gap-2'>
          <img src={CheckIcon} alt="Check Icon" />
          <span className="font-bold text-secondary-01 text-base">{item.title} :</span>
        </div>
        <span className="text-secondary-01 text-base pl-7">{item.content}</span>
      </li>
    );
  }

  // Case 2: For items that only have a 'title' (like Key Benefits and Applications)
  if ('title' in item) {
     return (
      <li className={`flex items-center gap-3 p-3 rounded-full ${backgroundClass}`}>
        <img src={CheckIcon} alt="Check Icon" />
        <span className="font-semibold text-secondary-01 text-base">{item.title}</span>
      </li>
    );
  }

  return null; 
};

export default ListItem;