import { useQueries } from "@tanstack/react-query";
import useApiServices from "../hooks/use-api-services";
import axiosInstance from "../../utils/axios";
import type { AxiosResponse } from "axios";

// A generic Media type for images
export interface Media {
  id: number;
  url: string;
  type: string;
}

// FOR /api/types RESPONSE
export interface MainType {
  id: number;
  name: string;
  description: string;
}

export interface MainTypesListResponse {
  status: string;
  data: MainType[];
  message: string;
}

// FOR /api/types/{id} RESPONSE
export interface ProductForMenu {
  id: number;
  name: string;
  mini_description: string;
  images: Media[]; 
}

export interface SubTypeDetails {
  name: string;
  parent_id: number;
  description: string;
  media: Media[];
  products: ProductForMenu[];
}

export interface SingleSubTypeResponse {
  status: string;
  data: SubTypeDetails;
  message: string;
}

// Interfaces for the final composed MegaMenu structure 
export interface ComposedProductLink {
    id: number;
    text: string;
    path: string;
}

export interface ComposedSubgroup {
    id: number;
    title: string;
    path: string;
    links: ComposedProductLink[];
}

export interface ComposedMegaMenuColumn {
    title: string;
    subgroups: ComposedSubgroup[];
}


export const TypeEndpoints = {
  list: "/types",
  details: "/types",
};

export const useTypesApi = () => {
  const { useGetListService, useGetItemService } = useApiServices({ axiosInstance });

  // Fetches the list of MAIN types from /types
  const useGetMainTypes = () => {
    return useGetListService<MainTypesListResponse, { filter: string }>({ 
      url: TypeEndpoints.list,
      params: { filter: 'main' } 
    });
    };

  // Fetches details for a SINGLE sub-type by its ID from /types/{id}
  const useGetSubTypeDetails = (id: number) => {
    return useGetItemService<SingleSubTypeResponse>({
      url: TypeEndpoints.details,
      id: id.toString(),
    });
  };

  // New hook to fetch details for multiple sub-types efficiently
  const useGetSubTypesDetails = (ids: (number | string)[]) => {
    return useQueries({
      queries: ids.map(id => {
        return {
          queryKey: ['sub-type-details', id],
          queryFn: async () => {
            const response: AxiosResponse<SingleSubTypeResponse> = await axiosInstance.get(`${TypeEndpoints.details}/${id}`);
            return { id: Number(id), ...response.data.data };
          },
          enabled: ids.length > 0,
        };
      })
    });
  };

  return {
    useGetMainTypes,
    useGetSubTypeDetails,
    useGetSubTypesDetails, 
  };
};