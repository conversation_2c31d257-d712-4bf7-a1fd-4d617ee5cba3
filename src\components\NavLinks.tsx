import { useState, useEffect, useRef, useMemo } from "react";
import { Link, NavLink, useLocation } from "react-router-dom";
import { type NavLinkItem, type MegaMenuColumn } from "../data/NavLinkData";
import ProductsMegaMenu from "./ProductsMegaMenu";
import { useTypesApi } from "../services/api/use-type-api";
import { useProductsApi } from "../services/api/use-product-api";

const NavLinks: React.FC<{
  links: NavLinkItem[];
  isMobileMenu: boolean;
  onLinkClick?: () => void;
}> = ({ links, isMobileMenu, onLinkClick }) => {
  const location = useLocation();
  const [openMenu, setOpenMenu] = useState<string | null>(null);
  const menuRefs = useRef<{ [key: string]: HTMLLIElement | null }>({});
  const [expandedLists, setExpandedLists] = useState<{
    [key: string]: boolean;
  }>({});
  const initialDisplayCount = 2;

  const { useGetMainTypes, useGetSubTypesDetails } = useTypesApi();
  const { useGetProducts } = useProductsApi();

  const { data: mainTypesResponse, isLoading: isLoadingTypes } =
    useGetMainTypes();
  // Note: The generic type here for useGetProducts was removed as it's not needed for this logic.
  const { data: allProductsResponse, isLoading: isLoadingProducts } =
    useGetProducts({ params: { per_page: 50 } });

  const subTypeIds = useMemo(() => {
    if (!allProductsResponse?.data) return [];
    const ids = allProductsResponse.data.map((p) => p.type.id);
    return [...new Set(ids)];
  }, [allProductsResponse]);

  const subTypeDetailsQueries = useGetSubTypesDetails(subTypeIds);
  const isLoadingSubTypes = subTypeDetailsQueries.some((q) => q.isLoading);

  const megaMenuDataFromApi = useMemo((): MegaMenuColumn[] => {
    const areAllQueriesLoaded =
      !isLoadingTypes && !isLoadingProducts && !isLoadingSubTypes;
    if (!areAllQueriesLoaded || !mainTypesResponse?.data) {
      return [];
    }

    const successfulSubTypes = subTypeDetailsQueries
      .filter((query) => query.isSuccess && query.data)
      .map((query) => query.data!);

    return mainTypesResponse.data
      .map((mainType): MegaMenuColumn => {
        const subgroups = successfulSubTypes
          .filter((subType) => subType.parent_id === mainType.id)
          .map((subType) => {
            const productLinks = subType.products.map((product) => ({
              id: product.id,
              text: product.name,
              path: `/products/${product.id}`,
            }));

            return {
              id: subType.id,
              title: subType.name,
              path: `/category/${subType.id}`,
              links: productLinks,
            };
          });

        return {
          title: mainType.name,
          subgroups: subgroups,
        };
      })
      .filter((column) => column.subgroups.length > 0);
  }, [
    mainTypesResponse,
    subTypeDetailsQueries,
    isLoadingTypes,
    isLoadingProducts,
    isLoadingSubTypes,
  ]);

  const isLoading = isLoadingTypes || isLoadingProducts || isLoadingSubTypes;

  const dynamicNavLinks = useMemo(() => {
    return links.map((link) => {
      if (link.text === "Products") {
        return { ...link, megaMenuColumns: megaMenuDataFromApi };
      }
      return link;
    });
  }, [links, megaMenuDataFromApi]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        openMenu &&
        menuRefs.current[openMenu] &&
        !menuRefs.current[openMenu]?.contains(event.target as Node)
      ) {
        setOpenMenu(null);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [openMenu]);

  const handleMenuToggle = (e: React.MouseEvent, linkText: string) => {
    e.preventDefault();
    setOpenMenu((prev) => (prev === linkText ? null : linkText));
  };

  const handleSubMenuLinkClick = () => {
    setOpenMenu(null);
    if (onLinkClick) {
      onLinkClick();
    }
  };

  const handleToggleExpand = (listId: string) => {
    setExpandedLists((prev) => ({ ...prev, [listId]: !prev[listId] }));
  };

  const handleScroll = (
    event: React.MouseEvent<HTMLAnchorElement>,
    path: string
  ) => {
    if (location.pathname === "/") {
      event.preventDefault();
      const id = path.substring(2);
      const element = document.getElementById(id);
      if (element) {
        element.scrollIntoView({ behavior: "smooth", block: "start" });
        window.history.pushState(null, "", path);
      }
    }
    if (onLinkClick) {
      onLinkClick();
    }
  };

  const baseClassName =
    "py-3 lg:py-[29.5px] lg:px-2 xl:px-3 text-center text-base transition-all duration-300 ease-in-out hover:text-primary-01";

  return (
    <ul
      className={` w-full flex ${
        isMobileMenu ? "flex-col" : "flex-row items-center lg:gap-4 "
      }`}
    >
      {dynamicNavLinks.map((link, index) => (
        <li
          key={index}
          ref={(el) => {
            menuRefs.current[link.text] = el;
          }}
          className={`${
            isMobileMenu
              ? "w-full border-b border-gray-700 last:border-b-0"
              : link.type === "megaMenu"
              ? "static"
              : "relative"
          }`}
        >
          {link.type === "page" || link.type === "section" ? (
            link.type === "page" ? (
              <NavLink
                to={link.path!}
                onClick={onLinkClick}
                className={({ isActive }) =>
                  isMobileMenu
                    ? `block w-full p-4 ${
                        isActive ? "font-bold text-primary-01" : "text-white"
                      }`
                    : `${baseClassName} ${
                        isActive
                          ? "font-bold text-primary-01 border-primary-01"
                          : "text-white border-transparent"
                      }`
                }
                end={link.path === "/"}
              >
                {link.text}
              </NavLink>
            ) : (
              <Link
                to={link.path!}
                onClick={(e) => handleScroll(e, link.path!)}
                className={
                  isMobileMenu
                    ? "block w-full p-4 text-white"
                    : `${baseClassName} text-white border-transparent cursor-pointer`
                }
              >
                {link.text}
              </Link>
            )
          ) : (
            <button
              onClick={(e) => handleMenuToggle(e, link.text)}
              className={
                isMobileMenu
                  ? `flex justify-between items-center w-full p-4 text-left 
              ${
                openMenu === link.text
                  ? "font-bold text-primary-01"
                  : "text-white"
              }`
                  : `${baseClassName} ${
                      openMenu === link.text
                        ? "font-bold text-primary-01 border-primary-01"
                        : "text-white border-transparent"
                    } cursor-pointer`
              }
            >
              <span>{link.text}</span>
              {isMobileMenu && (
                <svg
                  className={`w-4 h-4 transition-transform duration-300 ${
                    openMenu === link.text ? "rotate-180" : "rotate-0"
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
              )}
            </button>
          )}

          {isMobileMenu &&
            (link.type === "dropdown" || link.type === "megaMenu") && (
              <div
                className={`transition-all duration-500 ease-in-out overflow-hidden ${
                  openMenu === link.text ? "max-h-[3000px]" : "max-h-0"
                }`}
              >
                <div className="p-2 bg-secondary-02 ">
                  {link.type === "dropdown" && (
                    <ul className="flex flex-col pl-4">
                      {link.children?.map((child, index) => (
                        <li key={index}>
                          <NavLink
                            to={child.path!}
                            onClick={handleSubMenuLinkClick}
                            className="block p-3 text-sm text-white hover:text-primary-01"
                          >
                            {child.text}
                          </NavLink>
                        </li>
                      ))}
                    </ul>
                  )}
                  {link.type === "megaMenu" &&
                    (link.megaMenuColumns && link.megaMenuColumns.length > 0 ? (
                      <div className="flex flex-col">
                        {link.megaMenuColumns.map((column, index) => (
                          <div key={index} className="mb-4">
                            <h3 className="px-2 pb-2 font-bold text-base text-white border-b border-gray-600 mb-2">
                              {column.title}
                            </h3>

                            <div className="pl-2">
                              {column.subgroups.map((subgroup, s_index) => {
                                const listId = `mobile-${column.title}-${subgroup.title}`;
                                const isExpandable =
                                  subgroup.links.length > initialDisplayCount;
                                const isExpanded = !!expandedLists[listId];
                                const linksToShow =
                                  isExpandable && !isExpanded
                                    ? subgroup.links.slice(
                                        0,
                                        initialDisplayCount
                                      )
                                    : subgroup.links;

                                return (
                                  <div key={s_index} className="mt-1">
                                    {subgroup.path ? (
                                      <NavLink
                                        to={subgroup.path}
                                        onClick={handleSubMenuLinkClick}
                                        className="block p-2 font-semibold text-sm text-gray-200 hover:text-primary-01"
                                      >
                                        {subgroup.title}
                                      </NavLink>
                                    ) : (
                                      <h4 className="p-2 font-semibold text-sm text-gray-200">
                                        {subgroup.title}
                                      </h4>
                                    )}

                                    {subgroup.links.length > 0 && (
                                      <>
                                        <ul className="flex flex-col pl-3">
                                          {linksToShow.map(
                                            (megaLink, ml_index) => (
                                              <li key={ml_index}>
                                                {megaLink.path && (
                                                  <NavLink
                                                    to={megaLink.path}
                                                    onClick={
                                                      handleSubMenuLinkClick
                                                    }
                                                    className="block p-2 text-sm text-gray-400 hover:text-primary-01"
                                                  >
                                                    {megaLink.text}
                                                  </NavLink>
                                                )}
                                              </li>
                                            )
                                          )}
                                        </ul>
                                        {isExpandable && (
                                          <button
                                            onClick={() =>
                                              handleToggleExpand(listId)
                                            }
                                            className="text-primary-01 text-xs mt-1 ml-3 p-1 hover:underline focus:outline-none cursor-pointer"
                                          >
                                            {isExpanded
                                              ? "See Less"
                                              : "See All"}
                                          </button>
                                        )}
                                      </>
                                    )}
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="p-4 text-center text-primary-01">
                        There are no products to display at the moment.
                      </div>
                    ))}
                </div>
              </div>
            )}

          {!isMobileMenu &&
            link.type === "dropdown" &&
            openMenu === link.text && (
              <div className="absolute top-full left-0 mt-1 w-60 bg-secondary-02 rounded-md z-20 shadow-sm shadow-primary-01">
                <ul className="flex flex-col p-2">
                  {link.children?.map((child, index) => (
                    <li key={index}>
                      <NavLink
                        to={child.path!}
                        onClick={handleSubMenuLinkClick}
                        className="block px-4 py-2 text-sm text-white border-b border-transparent hover:text-primary-01 "
                      >
                        {child.text}
                      </NavLink>
                    </li>
                  ))}
                </ul>
              </div>
            )}

          {!isMobileMenu &&
            link.type === "megaMenu" &&
            openMenu === link.text &&
            (link.megaMenuColumns && link.megaMenuColumns.length > 0 ? (
              <ProductsMegaMenu
                columns={link.megaMenuColumns}
                onLinkClick={handleSubMenuLinkClick}
              />
            ) : (
              !isLoading && (
                <div className="absolute top-full left-1/2 -translate-x-1/2 mt-1 w-auto bg-secondary-02 rounded-md z-20 shadow-sm shadow-primary-01 p-8">
                  <p className="text-white whitespace-nowrap">
                    {" "}
                    There are no products to display at the moment.
                  </p>
                </div>
              )
            ))}
        </li>
      ))}
    </ul>
  );
};

export default NavLinks;
