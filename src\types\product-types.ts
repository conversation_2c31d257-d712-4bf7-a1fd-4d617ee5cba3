export interface Product {
  id: number;
  imageUrl: string;
  title: string;
  description: string;
  learnMoreUrl: string;
}

export interface Breadcrumb { name: string; url?: string; }
export interface ProductCategory { name: string; url: string; }
export interface ProductTag { name: string; url: string; }

export interface ProductDetail {
  id: string;
  name: string;
  breadcrumbs: Breadcrumb[];
  description: string;
  imageGallery: string[];
  categories: ProductCategory[];
  tags: ProductTag[];
}