import { useState } from "react";
import NavLinks from "./NavLinks";
import { Link } from "react-router-dom";
import type { NavLinkItem } from "../data/NavLinkData";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faBarsStaggered, faXmark } from "@fortawesome/free-solid-svg-icons";

interface NavBarProps {
  logoImg: string;
  links: NavLinkItem[];
}
const NavBar: React.FC<NavBarProps> = ({ logoImg, links }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen((prev) => !prev);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <nav className=" fixed z-50 w-screen bg-secondary-02 ">
      <div className="2xl:container mx-auto flex justify-between items-center md:px-[60px] px-5 xl:px-main-padding-large h-20 ">
        <Link to={"/"}>
          <img src={logoImg} alt="logo" loading="lazy" />
        </Link>
        <div className="hidden lg:flex">
          <NavLinks isMobileMenu={false} links={links} />
        </div>
        <button
          onClick={toggleMenu}
          className="lg:hidden z-50 cursor-pointer p-3"
        >
          {isMenuOpen ? (
            <FontAwesomeIcon
              icon={faXmark}
              className="scale-200 text-white hover:text-primary-01 transition-colors duration-200 cursor-pointer"
            />
          ) : (
            <FontAwesomeIcon
              icon={faBarsStaggered}
              className="scale-200 text-white hover:text-primary-01 transition-colors duration-200 cursor-pointer"
            />
          )}
        </button>
        <div
          className={`
          flex flex-col items-start px-5 pt-24 h-screen overflow-y-auto
          fixed top-0 right-0 w-full
          bg-secondary-02 
          transition-transform duration-300 ease-in-out
          lg:hidden
          ${isMenuOpen ? "translate-x-0" : "translate-x-full"}
        `}
        >
          <NavLinks isMobileMenu={true} onLinkClick={closeMenu} links={links} />
        </div>
      </div>
    </nav>
  );
};

export default NavBar;
