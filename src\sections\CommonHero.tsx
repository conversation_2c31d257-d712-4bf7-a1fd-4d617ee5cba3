
import { useState, useEffect, useCallback } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons';
import CommonButton from '../components/CommonButton';

interface Slide {
  image: string;
  title?: string;
  description?: string;
}

interface HeroProps {
  slides?: Slide[];
  image?: string;
  title?: string;
  description?: string;
  yellowLineImage?: string;
  subHero?: boolean;
  isCenter?: boolean;
  dynamicText?: boolean;
  onSlideChange?: (index: number) => void;
}

const CommonHero: React.FC<HeroProps> = ({ 
  slides, 
  image, 
  title, 
  description, 
  yellowLineImage, 
  subHero, 
  isCenter,
  dynamicText = false,
  onSlideChange 
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const imageSources = slides || (image ? [{ image, title, description }] : []);


  useEffect(() => {
    if (onSlideChange) {
      onSlideChange(currentIndex);
    }
  }, [currentIndex, onSlideChange]);

  const goToPrevious = () => {
    const isFirstSlide = currentIndex === 0;
    const newIndex = isFirstSlide ? imageSources.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);
  };

  const goToNext = useCallback(() => {
    if (imageSources.length <= 1) return;
    const isLastSlide = currentIndex === imageSources.length - 1;
    const newIndex = isLastSlide ? 0 : currentIndex + 1;
    setCurrentIndex(newIndex);
  }, [currentIndex, imageSources.length]);
  
  const goToSlide = (slideIndex: number) => {
    setCurrentIndex(slideIndex);
  };

  useEffect(() => {
    if (imageSources.length > 1) {
      const slideInterval = setInterval(goToNext, 5000);
      return () => clearInterval(slideInterval);
    }
  }, [goToNext, imageSources.length]);

  const currentTitle = dynamicText && imageSources[currentIndex]?.title ? imageSources[currentIndex].title : title;
  const currentDescription = dynamicText && imageSources[currentIndex]?.description ? imageSources[currentIndex].description : description;

  return (
    <>
      <svg className="absolute w-0 h-0">
        <defs>
          <clipPath id="hero-clip-path" clipPathUnits="objectBoundingBox">
            <path d="M0,0 H1 V0.75 Q0.5,1 0,0.75 Z" />
          </clipPath>
        </defs>
      </svg>
      
      <div className="relative h-[110vh] w-full  ">
        {/* Layer 1: The Yellow Vector */}
        {imageSources.length === 1 && yellowLineImage && (
          <div className="absolute inset-0 z-10 [clip-path:url(#hero-clip-path)] [-webkit-clip-path:url(#hero-clip-path)]">
            <img
              src={yellowLineImage}
              alt="Yellow decorative line"
              className="w-full h-full object-cover"
              loading="lazy"
            />
          </div>
        )}
        {/* Layer 2: The Main Image/Slider */}
        <div
          className={`absolute inset-0 z-20 [clip-path:url(#hero-clip-path)] [-webkit-clip-path:url(#hero-clip-path)] overflow-hidden transition-transform duration-300
          ${imageSources.length === 1 ? '-translate-y-4' : ''}`}
        >
          <div
            className="flex h-full transition-transform duration-700 ease-in-out"
            style={{ transform: `translateX(-${currentIndex * 100}%)` }}
          >
            {imageSources.map((slide, index) => (
              <div key={index} className="relative h-full w-full flex-shrink-0">
                <img
                  src={slide.image}
                  alt={`Slide ${index + 1}`}
                  className="w-full h-full object-cover object-center"
                  loading="lazy"
                />
              </div>
            ))}
          </div>
        {/* Layer 3: OVERLAY LAYER */}
        {subHero && (
          <div className="absolute inset-0 z-25 bg-black/50 [clip-path:url(#hero-clip-path)] [-webkit-clip-path:url(#hero-clip-path)]"></div>
        )}
        </div>
        {/* Layer 4: Text */}
        <div data-aos="fade-right" 
          className={` ${!subHero ? 'lg:max-w-1/2 2xl:mx-auto' : 'mx-auto'} 2xl:container  h-[calc(100vh-80px)] absolute inset-0 z-30 flex flex-col justify-center 
          ${isCenter ? 'items-center text-center' : 'items-start'}  pt-20 px-5 md:pl-[60px] xl:pl-main-padding-large `}>
          <div className={`${isCenter ? 'max-w-full' : 'max-w-xl xl:max-w-3xl '} `}>
            <h1 className={` font-black leading-[150%] text-primary-01 mb-3 
              ${!subHero ? 'font-nextstep  text-2xl sm:text-4xl md:text-5xl xl:text-6xl ' : ' text-2xl sm:text-3xl md:text-4xl xl:text-5xl'} `}>
              {currentTitle}
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl xl:text-[32px] text-white-02 leading-[150%] mb-4 xl:mb-7">
              {currentDescription}
            </p>
            {!subHero && (
                <CommonButton path='#contact' text=' Contact Us' style='w-fit bg-primary-01 py-2 px-4 lg:py-4 lg:px-8  hover:font-bold' textColor='text-secondary-01' />
            )}
            
          </div>
        </div>
        {/* Layer 5: Navigation Controls */}
        {imageSources.length > 1 && (
          <div className="absolute z-40 bottom-[15%] left-1/2 -translate-x-1/2 flex flex-col items-center gap-6 xl:gap-12">
            <div className='flex gap-6'>
              <button
                onClick={goToPrevious}
                className={`h-9 w-9 lg:h-11 lg:w-11 p-2.5 rounded-full transition-all ease-in-out cursor-pointer hover:bg-primary-01 bg-white flex items-center justify-center`}
                aria-label="Previous Slide"
              >
                <FontAwesomeIcon icon={faChevronLeft} className="h-5 w-5 lg:h-6 lg:w-6" />
              </button>
              <button
                onClick={goToNext}
                className={`h-9 w-9 lg:h-11 lg:w-11 p-2.5 rounded-full transition-all ease-in-out cursor-pointer hover:bg-primary-01 bg-white flex items-center justify-center`}
                aria-label="Next Slide"
              >
                <FontAwesomeIcon icon={faChevronRight} className="h-5 w-5 lg:h-6 lg:w-6" />
              </button>
            </div>
            <div className='flex gap-4'>
              {imageSources.map((_, slideIndex) => (
                <button
                  key={slideIndex}
                  onClick={() => goToSlide(slideIndex)}
                  className={`h-3 rounded-full transition-all duration-300 cursor-pointer ${slideIndex === currentIndex ? 'bg-primary-01 p-2 w-8' : 'bg-white w-3'}`}
                  aria-label={`Go to slide ${slideIndex + 1}`}
                ></button>
              ))}
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default CommonHero;
