import { useEffect, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons';
import type { Category } from '../../../types/portfolio-types';

interface FilterTabsProps {
  categories: Category[];
  activeCategory: string;
  onSelectCategory: (id: string) => void;
}

export const FilterTabs: React.FC<FilterTabsProps> = ({ categories, activeCategory, onSelectCategory }) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (scrollContainerRef.current) {
      const activeButton = scrollContainerRef.current.querySelector(`[data-category-id='${activeCategory}']`);
      if (activeButton) {
        activeButton.scrollIntoView({
          behavior: 'smooth',
          inline: 'center',
          block: 'nearest',
        });
      }
    }
  }, [activeCategory]);

  const handleArrowClick = (direction: 'left' | 'right') => {
    const currentIndex = categories.findIndex(cat => cat.id === activeCategory);
    if (currentIndex === -1) return;

    const nextIndex = direction === 'left' ? currentIndex - 1 : currentIndex + 1;

    if (nextIndex >= 0 && nextIndex < categories.length) {
      onSelectCategory(categories[nextIndex].id);
    }
  };

  if (!categories || categories.length === 0) {
    return null;
  }
  
  const currentIndex = categories.findIndex(cat => cat.id === activeCategory);
  const isAtStart = currentIndex === 0;
  const isAtEnd = currentIndex === categories.length - 1;

  return (
    <div className="2xl:container mx-auto relative w-full flex items-center justify-center gap-x-2 md:gap-x-4 px-5 
    md:px-[60px] xl:px-main-padding-large py-5 md:py-[60px]">

      <button 
        onClick={() => handleArrowClick('left')} 
        disabled={isAtStart}
        className="flex-shrink-0 w-8 h-8 md:w-12 md:h-12  bg-primary-01/90 hover:bg-primary-01 text-secondary-01 
        rounded-full flex items-center justify-center transition-all duration-300 z-10 cursor-pointer
        disabled:opacity-40 disabled:cursor-not-allowed"
        aria-label="Scroll Left"
      >
        <FontAwesomeIcon icon={faChevronLeft} className="w-4 h-4" />
      </button>

      <div className="flex-grow overflow-x-auto whitespace-nowrap scroll-smooth no-scrollbar" ref={scrollContainerRef}>
        {categories.map((category) => (
          <button 
            key={category.id}
            data-category-id={category.id}
            onClick={() => onSelectCategory(category.id)}
            className={`inline-block text-center py-2 md:py-3 px-3 md:px-6 rounded-full mx-1 transition-all duration-300 cursor-pointer
              text-sm md:text-base
              ${activeCategory === category.id
                ? 'bg-primary-01 text-secondary-01  border border-primary-01 font-semibold'
                : 'bg-white text-secondary-01 border border-gray-400 hover:bg-gray-100' 
              }`
            }
          >
            {category.title}
          </button>
        ))}
      </div>

      <button 
        onClick={() => handleArrowClick('right')} 
        disabled={isAtEnd}
        className="flex-shrink-0 w-8 h-8 md:w-12 md:h-12 bg-primary-01/90 hover:bg-primary-01 text-secondary-01 
        rounded-full flex items-center justify-center transition-all duration-300 z-10 cursor-pointer
        disabled:opacity-40 disabled:cursor-not-allowed"
        aria-label="Scroll Right"
      >
        <FontAwesomeIcon icon={faChevronRight} className="w-4 h-4"/>
      </button>
    </div>
  );
};