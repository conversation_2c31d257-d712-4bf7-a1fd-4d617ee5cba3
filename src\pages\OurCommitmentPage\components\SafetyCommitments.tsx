import SectionTitle from "../../../components/SectionTitle";

interface CommitmentItem {
  title: string;
  points: string[];
}

interface SafetyCommitmentsProps {
  mainTitle: string;
  imageSrc: string;
  commitments: CommitmentItem[];
}

const SafetyCommitments: React.FC<SafetyCommitmentsProps> = ({
  mainTitle,
  imageSrc,
  commitments,
}) => {
  return (
    <section className="2xl:container mx-auto p-4 md:p-[60px] xl:p-main-padding-large bg-white">
         <SectionTitle title={mainTitle} className='text-start w-fit gap-1 md:gap-3'/>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8  mt-10">
          <div className="hidden lg:flex lg:col-span-1  items-center justify-center">
            <img 
              src={imageSrc} 
              alt="Safety Commitments" 
              className="w-full h-auto object-contain"
              loading="lazy" 
            />
          </div>
          <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-10">
            {commitments.map((item, index) => (
              <div key={index}    className={index === commitments.length - 1 ? 'md:col-span-2' : ''}>
                <h3 className={`text-xl font-semibold mb-3 ${index % 4 < 2 ? 'text-primary-01' : 'text-secondary-01'}`}>
                  {item.title}
                </h3>
                 <ul className="space-y-2 list-disc list-outside pl-5 text-secondary-01">
                  {item.points.map((point, pIndex) => (
                    <li key={pIndex}>{point}</li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        
        </div>
    </section>
  );
};

export default SafetyCommitments;